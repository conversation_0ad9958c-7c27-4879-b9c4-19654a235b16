{"name": "post-security-manager", "version": "1.1.369-SNAPSHOT", "description": "Post Security Manager Service - Handles security of offers", "main": "main.js", "scripts": {"test": "npx gulp test", "start": "node build/src/main.js", "build": "npx gulp build", "dist": "npx gulp dist", "watch": "npx gulp watch", "docker-build": "yarn dist && NAME=post-security-manager docker-build", "docker-publish": "yarn docker-build && NAME=post-security-manager docker-push", "db:schema-drop": "typeorm schema:drop -c master", "db:schema-sync": "typeorm schema:sync -c master", "db:run-migration": "typeorm migration:run -c master", "db:revert-migration": "typeorm migration:revert -c master", "db:rebuild-test": "npm run db:schema-drop && npm run db:schema-sync && npm run db:run-migration", "typeorm": "npx ts-node ./node_modules/typeorm/cli.js", "apidocs:generate": "npx ./node_modules/apidoc/bin/apidoc -f \".*\\.ts$\"  --output docs/"}, "releaseme": {"steps": ["setReleaseVersion", "commitReleaseVersion", "tagRelease", "setNextVersion", "commitNextVersion", "pushChanges"]}, "docker-registry": "277983268692.dkr.ecr.us-east-1.amazonaws.com", "repository": {"type": "git", "url": "git+https://github.com/LoyaltyOne/post-security-manager.git"}, "husky": {"hooks": {"pre-push": "npm run test && gulp tslint "}}, "author": "", "license": "ISC", "dependencies": {"apidoc": "^0.17.7", "aws-sdk": "^2.1692.0", "bluebird": "^3.5.3", "bunyan": "^1.8.12", "class-validator": "^0.14.0", "convict": "^6.2.3", "lodash": "^4.17.11", "moment": "^2.24.0", "mysql2": "^3.14.0", "nock": "^10.0.6", "path": "^0.12.7", "reflect-metadata": "^0.1.13", "request": "^2.88.0", "request-promise": "^4.2.2", "restify": "^10.0.0", "restify-cors-middleware": "^1.1.1", "restify-errors": "^6.1.1", "restify-health-router": "^1.0.3", "restify-router": "^0.5.1", "tslint": "^5.12.0", "typedi": "^0.8.0", "typeorm": "^0.2.12"}, "devDependencies": {"@types/bluebird": "^3.5.25", "@types/chai": "^4.1.7", "@types/hapi": "^17.8.2", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.5", "@types/nock": "^9.3.1", "@types/node": "^10.12.19", "@types/restify-errors": "^4.3.3", "@types/sinon": "^7.0.5", "chai": "^4.2.0", "del": "^3.0.0", "docker-build-run-push": "^3.0.0", "gulp": "^4.0.0", "gulp-install": "^1.1.0", "gulp-load-plugins": "^1.5.0", "gulp-mocha": "^8.0.0", "gulp-nodemon": "^2.4.2", "gulp-tslint": "^8.1.3", "gulp-typescript": "^5.0.0", "husky": "^1.3.1", "mocha": "^5.2.0", "nodemon": "^1.18.9", "npx": "^10.2.0", "rimraf": "^2.6.3", "sinon": "^7.2.3", "ts-node": "^8.0.2", "tslint-config-prettier": "^1.17.0", "typescript": "^5.3.3"}}