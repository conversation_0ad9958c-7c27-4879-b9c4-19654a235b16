"use strict";

const gulp = require("gulp");
const plugins = require("gulp-load-plugins")();
const ts = require("gulp-typescript");
const del = require("del");
const tslint = require("gulp-tslint");
const nodemon = require("gulp-nodemon");
const tsProject = ts.createProject("tsconfig.json");

const paths = {
  src: "src",
  build: "./build/",
  dist: "./dist/",
  unitTestFiles: ["test/unit/**/*.spec.ts"]
};

// Files that will be packaged for deployment
const distFiles = [
  "package.json",
  `${paths.build}/**/*`,
  "node_modules/**/*",
  "ormconfig.js"
];

const jsonFiles = [
  `${paths.src}/config/*.json`,
  `${paths.src}/mockdata/*.json` // TODO: this should be like environment specific
];

gulp.task("clean", () => {
  return del([paths.build, paths.dist]);
});

gulp.task("tslint", () => {
  return gulp
    .src("src/**/*.ts")
    .pipe(
      tslint({
        formatter: "prose"
      })
    )
    .pipe(tslint.report());
});

gulp.task("test", () => {
  return gulp.src(paths.unitTestFiles).pipe(
    plugins.mocha({
      reporter: "spec",
      require: ["ts-node/register"]
    })
  );
});

gulp.task("compile", () => {
  return tsProject
    .src()
    .pipe(tsProject())
    .js.pipe(gulp.dest(paths.build));
});

gulp.task("copyJSON", () => {
  return gulp.src(jsonFiles, { base: "." }).pipe(gulp.dest("build"));
});

gulp.task("distPackage", () => {
  return gulp.src(distFiles, { base: "." }).pipe(gulp.dest("dist"));
});

gulp.task(
  "watch",
  gulp.series("clean", "tslint", "copyJSON", "compile", () => {
    var stream = nodemon({
      watch: [paths.src],
      script: "build/src/main.js",
      tasks: ["build"],
      ext: "ts,json",
      runOnChangeOnly: false
    });
    stream.on("restart", function () {
      console.log("Restarted!");
    });
    return stream;
  })
);

gulp.task("build", gulp.series("copyJSON", "tslint", "compile"));

gulp.task("dist", gulp.series("clean", "build", "distPackage"));

gulp.task("default", gulp.series("build"));
