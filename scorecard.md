- [x] Repo has a well written MD that covers the following :

  - what the purpose of the repo is
  - dependencies needed to run the repo
  - How to run the repo  
  - A link to the scorecard.md

- [ ] Repo has OpenAPI documentation for all public endpoints

- [ ] Repo follows standardized project structure 

- [x] Repo has testing with baseline coverage established

- [ ] Repo has code quality metrics with base level of compliance set

- [x] Repo owner indicated in the git topic

- [x] Repo is marked ‘business-critical’ in the git topic

- [x] Repo has a code owner file

- [ ] Repo has 80% coverage

- [ ] Grade “A” rating in code quality metrics

- [x] Test automation can be run during CI

- [ ] The CD pipeline requires no manual steps
