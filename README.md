# [Post Security Manager](https://github.com/LoyaltyOne/post-security-manager)

- [Post Security Manager](#post-security-manager)
  - [Project Overview](#project-overview)
    - [Environments](#environments)
  - [Setup/Installation](#setupinstallation)
    - [Setting up Local Aurora RDS](#setting-up-local-aurora-rds)
      - [Setting up Database Schemas/Seed Data](#setting-up-database-schemasseed-data)
    - [Setting up an Application Server](#setting-up-an-application-server)
  - [Codebase](#codebase)
    - [Dependencies](#dependencies)
      - [Runtime dependencies](#runtime-dependencies)
    - [Folder Structure](#folder-structure)
    - [Other Important Files](#other-important-files)
  - [Available Scripts](#available-scripts)
    - [yarn watch](#yarn-watch)
    - [yarn test](#yarn-test)
    - [setup_ssh_tunnels.sh](#setupsshtunnelssh)
  - [Development](#development)
    - [Debugging the server in Visual Studio Code](#debugging-the-server-in-visual-studio-code)
    - [API Gateway](#api-gateway)
  - [API Documentation](#api-documentation)
  - [Database/ORM](#databaseorm)
    - [TypeORM](#typeorm)
      - [Manual Migrations](#manual-migrations)
        - [Example: Running a Manual Migration on UAT](#example-running-a-manual-migration-on-uat)
    - [Configurations](#configurations)
  - [Cloudformation/Jenkins](#cloudformationjenkins)
    - [AWS](#aws)
      - [Environment Configurations](#environment-configurations)
      - [Aurora RDS](#aurora-rds)
    - [Jenkins](#jenkins)
      - [Available Jenkins Pipelines](#available-jenkins-pipelines)
      - [Scripts used by Jenkins](#scripts-used-by-jenkins)
      - [Other Scripts](#other-scripts)

---

## Project Overview

The Post Security Manager Service (PSM) provides permission based access to working with offers. It handles the following items:

- Group Management
  - Add/Delete a Group
  - Assign/Un-assign partners from group
- User Management
  - Assign/Un-assign a group to a user
  - Assign/Un-assign a partner to a user

This project runs in conjunction with the following projects:

- [Offer Submission Web](https://github.com/LoyaltyOne/offer-submission-web)
  - Front end UI for Managing Offers
- [POST API Gateway](https://github.com/LoyaltyOne/post-api-gateway)
  - API Gateway to connect services together
- [Offer Management v3](https://github.com/LoyaltyOne/offer-management-api)
  - API to handle Offers
- [Content Generation Service](https://github.com/LoyaltyOne/content-generation-service)
  - Dynamically retrieving copytext for an offer
- [Partners API](https://github.com/LoyaltyOne/airmiles-partner-api)
  - All Partners

### Environments

The Post Security Manager is hosted on the following environments:

- [Local](http://localhost:8082)
- Dev
- UAT
- Prod

### Scorecard
[Scorecard - level 1 - API](scorecard.md)

---

## Setup/Installation

Run local script which runs all required commands as part of one script.

```bash
$ ./local-start.sh
```

If above doesn't work, then try to run below steps one by one.

### Setting up Local Aurora RDS

A local MySQL Aurora RDS can be created by spinning up a container on **Docker** through Docker Compose.

```bash
$ docker-compose up
```

This will create a **Aurora MySQL Version 5.7 Database** running on a docker container which can be accessed via `port 3308`.

#### Setting up Database Schemas/Seed Data

Schema management and migrations are handled using **TypeORM** (see below for more information on this.)

The database can be populated either through running the application server itself (which on local, will always keep the database schema up to date with the code on startup), or by manually running the database synchronization and migration commands.

To run the commands manually:

```bash
# Synchronizes schemas in the database with the codebase
$ npm run db:schema-sync

# Seeds the database with provided migration data
$ npm run db:run-migration
```

### Setting up an Application Server

The application server that will be run is a Node server using the **Restify** framework.

```bash
# Follow instructions to install Yarn here: https://yarnpkg.com/lang/en/docs/install/#mac-stable

# On Macs:
$ brew install yarn

# Install Project Dependencies
$ yarn install
```

```bash
# Compile source code to build folder
$ npm run build

$ npm run start
```

---

## Codebase

### Dependencies

#### Runtime dependencies

The following dependencies are some of the key dependencies that will be bundled as part of the production final artifact.

- [apidoc](http://apidocjs.com/)
- [bluebird](https://github.com/petkaantonov/bluebird)

  Provides extended functionality for using promises

- [convict](https://www.npmjs.com/package/convict)

  Provides a configuration schema to set up application properties easily

- [class-validator](https://github.com/typestack/class-validator)

  Provides an way to add annotations to classes which can be used for validation

- [lodash](https://lodash.com/)

  Javascript Utility Library

- [mysql](https://github.com/mysqljs/mysql)

  Node.JS Javascript Client Implementing the MySQL Protocol (needed with TypeORM)

- [nock](https://github.com/nock/nock)

  Allows for mocking server calls

- [restify](http://restify.com/)

  Application Framework for NodeJS

- [typedi](https://github.com/typestack/typedi)

  Dependency Injection tool

- [typeorm](https://github.com/typeorm/typeorm)

  ORM for TypeScript & Javascript

### Folder Structure

This application follows the MVCS (Model, View, Controller, Services) framework.

- **Config**
  - \${ENV}.json
    - The Application configs for each environment.
    - Links to the external API endpoints
  - config.ts
    - Using **convictjs** to set up application configuration as well as **typedi** to create a singleton that the rest of the application can use for dependency injection
- **Controllers**
  - All controllers extend the **BaseController** which handles generic error handling.
  - All routing is handled within the respective controllers
  - **typedi** is used to make controllers singletons
- **Database**
  - used with TypeORM to create the initial connection to Aurora RDS
- **Entity**
  - All entity models under this folder are what **TypeORM** references when syncing data schemas to the Database. See [here](https://typeorm.io/#/entities) for more information.
  - Changes to entities will automatically be applied when the application runs (to prevent this, set `synchronize: false` under **ormconfig.js**)
  - Internal models that are not to be synced to the Database should not be stored here
- **Migration**
  - TypeORM does not explictly support seed data. To support it, the work around is to add seed data into a migration file and have TypeORM treat it as a migration. Read more [here](https://typeorm.io/#/migrations).
- **Model**
  - Internal Classes
- **Services**
  - **typedi** is used to make services singletons

### Other Important Files

- **ormconfig.js**
  - Sets up the Configuration information for TypeORM
  - Environment variables are used to overwrite properties for the different environments. Those environment files are found under `env/${ENV}/${ENV}.dev`
  - See [here](https://typeorm.io/#/using-ormconfig/using-ormconfigjs) for more information
- **cfn/**
  - Cloud formation template for the Aurora RDS is here
- **jenkins/**
  - Jenkins Jobs & Scripts for the jobs
- **env/**
  - CFT Configuration files as well as application configs for the environments
- **setup_ssh_tunnels.sh**
  - Shortcut script that can be run from an L1 Machine to tunnel into environments

---

## Available Scripts

In the project directory, you can run:

### yarn watch

Runs the app in the development mode and watches for changes to any of the \*.ts files<br>
Open [localhost:8082](localhost:8082/test) to view it in the browser.

The page will reload if you make edits.<br>
You will also see any lint errors in the console.

### yarn test

Runs unit tests for the services.

See the section about [deployment](#deployment) for more information.

### setup_ssh_tunnels.sh

A utility script that assists with setting up SSH tunnels for a specific environment, to access the Database and APIs. Sets up SSH tunnels to the RBAC + PSM databases as well as APIs.

**Note:** To tunnel into the environments, requires the respective bastion PEM key for that environment.

---

## Development

### Debugging the server in Visual Studio Code

To run the server in debug mode,

1. Click on the Debug icon in the Activity Bar on the side of VS Code (Ctrl+Shift+D)
2. Click on the dropdown to list the different Debug options.
3. There are 2 options:

- Launch Program: Launch the node server in debug mode.
- Mocha Tests: Launch the tests in debug mode.

### API Gateway

**Every endpoint** added to the POST-Security-manager project needs to also be **added to the [post-api-swagger.yml](https://github.com/LoyaltyOne/post-api-gateway/blob/master/swagger/post-api-swagger.yml)** in order for API Gateway to pick up the endpoints, otherwise you will be able to hit these endpoints.

```yaml
Example:
paths:
  /groups:
    get:
      tags:
        - groups-controller
      summary: View a list of groups(s)
      operationId: listGroupsUsingGET
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successfully retrieved list
          headers:
            Set-Cookie:
              type: "string"
        "401":
          description: Unauthorized
          headers:
            Set-Cookie:
              type: "string"
        "403":
          description: Forbidden
          headers:
            Set-Cookie:
              type: "string"
        "404":
          description: Not Found
          headers:
            Set-Cookie:
              type: "string"
        "500":
          description: Internal Server Error
          headers:
            Set-Cookie:
              type: "string"
      # security:
      #   - postAPIAuthorizer: []
      x-amazon-apigateway-integration:
        responses:
          "200":
            statusCode: "200"
          "400":
            statusCode: "400"
          "401":
            statusCode: "401"
          "404":
            statusCode: "404"
          default:
            statusCode: "500"
        uri: "http://${stageVariables.postSecurityDNS}/groups"
        passthroughBehavior: when_no_match
        connectionType: VPC_LINK
        connectionId: "${stageVariables.vpcLinkId}"
        httpMethod: GET
        type: http
```

---

## API Documentation

Documentation is auto generated using [ApiDoc](http://apidocjs.com). Documentation can be generated by running

```bash
npm run apidocs:generate
```

Documentation can be accessed by pulling the project locally, and loading `index.html` found under `docs/`.

---

## Database/ORM

### TypeORM

[TypeORM](http://typeorm.io/#/) is used to handle accessing and interacting with the database, as well as migrations.

On starting the server, typeorm is currently configured to automatically sync the
database schema. In order to seed with some test data, run:

```bash
yarn db:run-migration
```

Migration Data is located under the `migrations` folder.

New migrations can be created via:

```bash
typeorm migration:create -n MIGRATION_NAME
```

#### Manual Migrations

If manual migrations are needed to be run (primarily for uat and prod environments) you can run

```bash
jenkins/scripts/db-manual-migration.sh $ENVIRONMENT_TO_RUN migrate
```

to run the migration. This will trigger a DB Schema sync and data migration.

##### Example: Running a Manual Migration on UAT

```bash
  # Install necessary packages for the project
  # If on a machine that doesn't have yarn globally installed, you can run `npm install`
  $ yarn install
  # Compile the Schema/migration code from typescript to javascript as the TypeORM Cli runs against Javascript built code
  $ npm run build
  # From a L1 machine, setup SSH Tunnel to the specified environment's bastion from which we can access the RDS
  # Enter option 3 to setup the SSH tunnels for RDS Migration, uat as the environment and PSM as the DB to migrate.
  $ ./setup_ssh_tunnels.sh
  # Run the actual Migration - This will trigger a Schema sync and a data migration
  $ jenkins/scripts/db-manual-migration.sh uat migrate
```

### Configurations

Different configurations based on the environment can be set through the `ormconfig.js` file. Our current configuration in `ormconfig.js` has 3 different connection configs: `master`, `admin`, and `user`. The only differences between these 3 connection configs are the `username` and `password` attributes. As an example, we can modify the `master` connection config by modifying these environment variables:

```json
   "host": process.env.TYPEORM_HOST || "127.0.0.1",
   "port": process.env.TYPEORM_PORT || 3307,
   "username": process.env.TYPEORM_MASTER_USERNAME || "root",
   "password": process.env.TYPEORM_MASTER_PASSWORD || "root",
   "database": process.env.TYPEORM_DATABASE || "local-offers-rbac",
   "synchronize": process.env.TYPEORM_SYNCHRONIZE || false,
   "logging": process.env.TYPEORM_LOGGING || true,
   "ssl": process.env.TYPEORM_SSL_AMAZON_RDS === "true" ? "Amazon RDS" : false
```

#### SSL Config 

We are using https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem which is downloaded through our base docker image through get-rds-certs script.

---

## Cloudformation/Jenkins

### AWS

#### Environment Configurations

Deploying using AWS CFTs use **3 JSON configs per environment**.

- db.ENV.params.json
  - Configurations and Parameter Options for setting the Aurora RDS
- ENV.params.json
  - Primarily cluster information for ECS
- ENV.tags.json
  - AWS Tags

#### Aurora RDS

An Aurora RDS on AWS can be created through CloudFormation via the `db.rds-cluster.yml` combined with a params file for the corresponding environment.

Creating the RDS is through the `aws-cli` command below:

```bash
aws cloudformation create-stack
--stack-name "$NAME-OF-YOUR-STACK"
--template-body file://cnf/templates/db.rds-cluster.yml
--parameters file://env/$ENV/db.dev.params.json
--profile assumed_role
```

You can run the `generate-aurora-rds.sh` script found under the `jenkins/scripts` folder to generate the cluster for the different environments.

```bash
# Example: To generate the Aurora RDS on Dev
./jenkins/scripts/generate-aurora-rds.sh dev
```

**Note**: Permission to create a cluster through the AWS CLI requires the Loyalty SAML Token which can be generated locally [here](https://github.com/LoyaltyOne/apollo-platform/tree/master/scripts/saml-login) and selecting the `DevCrossAccountAccess-DevCrossAccountRole` when selecting the role.

**Note**: In order to create resources on uat and prod environments, they must be done through Jenkins. The Above SAML Token provides only read access for higher environments so you will not be able to create these resources through the CLI manually.

**Note**: We use a custom cluster parameter group so that we can explicitly set the collations for the Databases to be utf8 otherwise it defaults to the engine default which is latin. Both Post Security Manager and RBAC share this custom cluster parameter group but the scripts and jenkins jobs to set it up exist only in the offer-submission-api repository. The cloud formation template for this can be found [here](https://github.com/LoyaltyOne/offer-submission-api/blob/master/cfn/templates/db.rds-cluster-parameter-group.yml).

**Note**: Under the `env/${ENV}/${ENV}.env`, the **TYPEORM_HOST** needs to be manually updated with the writer endpoint for the RDS Clusters after they're created.

#### RDS accounts

The usernames and passwords for DEV and UAT are stored under the THINGS vault in 1Password. For prod they are stored in a vault managed by Team Dynamite.

Under the THINGS vault:
1. Dev_Post_Security_Manager_Database_Credentials
3. Uat_Post_Security_Manager_Database_Credentials

### Jenkins

#### Available Jenkins Pipelines

- Database Related
  - Create DB
    - used to create the Aurora RDS for the different environments
  - Sync DB
    - Syncs the current database schema for a specific environment with the latest schema version on master for the codebase
  - Migrate DB
    - Runs the migrations files (if any changes) for a specific environment
- Continuous Deployment (CD)
  - Found under the `deployments` folder
  - Deploys to a specific environment
  - Deploys code from the `build` folder
- Continuous Integration (CI)
  - Runs checks against PRs and creates version tags for merges into master

#### Scripts used by Jenkins

- Database Related:
  - Create Aurora RDS
    - Creates an Aurora MySQL RDS (runs the AWS CLI command with the `cfn/templates/db.rds-cluster.yml`)
  - Sync DB
    - Uses TypeORM to sync the Database Schema
  - Migrate DB
    - Uses TypeORM to run the database migrations
- Decrypting KMS Variables (env-decrypt)
  - Decrypts KMS Variables, which can then be exported into the command line for use

#### Other Scripts

- Manual DB Migration
  - 2019-04-29: As of this date, we had an issue running the `sync/migrate db commands` for the sandbox and prod environments. We are unable to determine if the cause is related to the fact that jobs are run from the deployments folder (the only way to have access to the upper environments) or if it is permission based issue because of the higher environments. As a work around for this issue, when using a **L1 Machine & tunneled into a higher environment**, you can run `jenkins/scripts/db-manual-migration.sh` which will sync and migrate the DB for a specific environment.
