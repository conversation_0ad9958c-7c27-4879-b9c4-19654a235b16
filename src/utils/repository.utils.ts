import Container from "typedi";
import { Repository, getRepository, getConnection } from "typeorm";
import { APP_DB_CONNECTION_NAME } from "../StartupServer";

export function getRepositoryFromContainer<T>(repositoryName: string, entityClass: any): Repository<T> {
  return Container.has(repositoryName) ?
    Container.get(repositoryName) : getConnection(APP_DB_CONNECTION_NAME).getRepository(entityClass);
}