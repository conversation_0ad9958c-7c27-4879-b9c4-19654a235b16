import * as bunyan from "bunyan";
import * as restify from "restify";

export function createLogger(config: any) {
  const streams = [];

  const log = config.get("log");

  if (log.stream.enabled) {
    streams.push({
      level: log.stream.level || "trace",
      stream: process.stdout,
    });
  }

  return bunyan.createLogger({
    name: "post-security-manager",
    serializers: {
      /*client_req: restify.bunyan.serializers.client_req,
      client_res: restify.bunyan.serializers.client_res,*/
      err: bunyan.stdSerializers.err,
      req: bunyan.stdSerializers.req,
      res: bunyan.stdSerializers.res,
    },
    streams,
  });
}
