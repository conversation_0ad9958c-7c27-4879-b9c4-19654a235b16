import {
  <PERSON><PERSON><PERSON>,
  <PERSON>reateDate<PERSON>olumn,
  PrimaryColumn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Colum<PERSON>
} from "typeorm";
import { User, USER_ID_LENGTH } from "./User";

export const PARTNER_ID_LENGTH = 36; // TODO: move this to a more appropriate location

@Entity("user_partner_direct")
export class UserPartnerDirect {
  @PrimaryColumn({
    name: "partner_id",
    type: "char",
    length: PARTNER_ID_LENGTH
  })
  public partnerId: string;

  @PrimaryColumn({
    name: "user_id",
    length: USER_ID_LENGTH
  })
  public userId: string;

  @ManyToOne(type => User)
  @JoinColumn({ name: "user_id" })
  public user?: User;

  @CreateDateColumn({
    name: "created_ts"
  })
  public createdDate?: Date;

  @Column({
    nullable: true,
    name: "created_by"
  })
  public createdBy?: string;
}
