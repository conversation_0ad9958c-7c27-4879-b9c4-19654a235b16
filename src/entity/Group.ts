import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  OneToMany,
  VersionColumn,
  UpdateDateColumn
} from "typeorm";
import { UserGroup } from "./UserGroup";
import { GroupPartnerMapping } from "./GroupPartnerMapping";
import { Partner } from "../model/Partner";
import { Length } from "class-validator";

@Entity("groups")
export class Group {
  @PrimaryGeneratedColumn()
  public id?: number;

  @Column({ length: 100, unique: true, nullable: false })
  @Length(3, 100)
  public name: string;

  @Column({ name: "is_editable", default: true })
  public isEditable: boolean;

  @Column({ name: "covers_all_partners", default: false })
  public coversAllPartners: boolean;

  @OneToMany(type => GroupPartnerMapping, partnerGroup => partnerGroup.group, {
    nullable: false
  })
  public partnerMappings?: GroupPartnerMapping[];

  public partners?: Partner[];

  @OneToMany(type => UserGroup, userGroup => userGroup.group, {
    nullable: false
  })
  public users?: UserGroup[];

  @CreateDateColumn({
    name: "created_ts",
    select: true
  })
  public createdDate?: Date;

  @UpdateDateColumn({
    name: "updated_ts"
  })
  public updatedDate?: Date;

  @VersionColumn()
  public version?: number;

  @Column({
    nullable: true,
    name: "created_by"
  })
  public createdBy?: string;

  @Column({
    nullable: true,
    name: "updated_by"
  })
  public updatedBy?: string;

  public constructor(requestGroup?: any) {
    if (requestGroup) {
      this.name = requestGroup.name;
    }
  }
}
