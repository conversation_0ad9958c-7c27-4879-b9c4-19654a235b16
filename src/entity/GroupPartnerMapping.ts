import {
  <PERSON><PERSON>ty,
  PrimaryColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Column
} from "typeorm";
import { Group } from "./Group";
import { PARTNER_ID_LENGTH } from "./UserPartnerDirect";

@Entity("group_partner_mapping")
export class GroupPartnerMapping {
  @PrimaryColumn({ name: "group_id" })
  public groupId: number;

  @ManyToOne(type => Group)
  @JoinColumn({ name: "group_id" })
  public group?: Group;

  @PrimaryColumn({
    name: "partner_id",
    type: "char",
    length: PARTNER_ID_LENGTH
  })
  public partnerId: string;

  @CreateDateColumn({
    name: "created_ts",
    select: true
  })
  public createdDate?: Date;

  @Column({
    nullable: true,
    name: "created_by"
  })
  public createdBy?: string;
}
