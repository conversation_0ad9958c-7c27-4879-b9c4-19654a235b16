import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  PrimaryColumn,
  ManyToOne,
  JoinC<PERSON>umn,
  Column
} from "typeorm";
import { User, USER_ID_LENGTH } from "./User";
import { Group } from "./Group";

@Entity("user_group")
export class UserGroup {
  @PrimaryColumn({
    name: "user_id",
    length: USER_ID_LENGTH
  })
  public userId: string;

  @PrimaryColumn({ name: "group_id" })
  public groupId: number;

  @ManyToOne(type => User)
  @JoinColumn({ name: "user_id" })
  public user: User;

  @ManyToOne(type => Group)
  @JoinColumn({ name: "group_id" })
  public group: Group;

  @CreateDateColumn({
    name: "created_ts"
  })
  public createdDate: Date;

  @Column({
    nullable: true,
    name: "created_by"
  })
  public createdBy?: string;
  
}
