import { <PERSON><PERSON><PERSON>, CreateDate<PERSON><PERSON>umn, PrimaryC<PERSON>umn, OneToMany } from "typeorm";
import { UserPartnerDirect } from "./UserPartnerDirect";
import { UserGroup } from "./UserGroup";
import { Length, Max } from "class-validator";

export const USER_ID_LENGTH = 255;

@Entity()
export class User {
  @PrimaryColumn({
    length: USER_ID_LENGTH
  })
  @Length(2, USER_ID_LENGTH)
  public id: string;

  @OneToMany(type => UserPartnerDirect, partner => partner.user, {
    nullable: false
  })
  public partners?: UserPartnerDirect[];

  @OneToMany(type => UserGroup, userGroup => userGroup.user, {
    nullable: false
  })
  public groups?: UserGroup[];

  @CreateDateColumn({
    name: "created_ts"
  })
  public createdDate?: Date;
}
