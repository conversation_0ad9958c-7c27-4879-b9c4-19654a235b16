export enum OfferType {
  Buy = "buy",
  Spend = "spend",
  AmCashEarn = "amCashEarn",
  AmCashDiscount = "amCashDiscount",
  Custom = "custom"
}

export enum Qualifier {
  Product = "product",
  Storewide = "storewide",
  Category = "category",
  CashRedemption = "cashRedemption",
  CashDiscount = "cashDiscount",
  Custom = "custom",
  Fuel = "fuel"
}

export enum AwardType {
  FlatMiles = "flatMiles",
  MultiplierMiles = "multiplierMiles",
  FlatDiscount = "flatDiscount",
  PercentDiscount = "percentDiscount",
  CashDiscount = "cashDiscount",
  CentsOff = "centsOff",
  Custom = "custom"
}

export enum MechanismType {
  NoAction = "noAction",
  BarcodeUPC = "barcodeUPC",
  BarcodeEAN = "barcodeEAN",
  Barcode39 = "barcodeCODE39",
  Barcode128 = "barcodeCODE128",
  PLU = "plu",
  CouponCode = "couponCode",
  Button = "button",
  LoadAndGo = "Load+Go",
  OptIn = "optIn"
}

export enum Region {
  AB,
  BC,
  MB,
  NB,
  NL,
  NS,
  NT,
  NU,
  ON,
  PE,
  QC,
  SK,
  YT,
  TB,
  ALL
}

export enum OfferStatus {
  Draft = "draft",
  Published = "published",
  Updated = "updated"
}

export enum OfferLimitation {
  NoLimit = "noLimit",
  PerCollector = "perCollector",
  PerCollectorPerTransaction = "perCollectorPerTransaction",
  PerCollectorPerDay = "perCollectorPerDay",
  Custom = "custom"
}

export interface ICopyText {
  awardLong: ILocalizedObject[];
  qualifierLong: ILocalizedObject[];
  awardShort: ILocalizedObject;
  qualifierShort: ILocalizedObject;
  legal: ILocalizedObject;
}

export interface IGetOffersResponse {
  content: IOfferFormModel[];
  totalCount: number;
  pageSize: number;
  pageNumber: number;
}

export interface IDeleteOffersResponse {
  deletedOffers: string[];
}

export interface IGetOffersCounts {
  draft: number;
  changesPending: number;
  staged: number;
  live: number;
  expired: number;
  disabled: number;
  total: number;
}

export interface IOfferFormModel {
  id?: string;
  duplicatedFrom?: string;
  contentfulId?: string;
  detailsId?: string;
  partnerId: string;
  partnerName: string;
  availability?: string[];
  partnerBaseEarnRate: number;
  baseCashRedemption: number;
  displayDate: string | Date;
  startDate: string | Date;
  endDate: string | Date;
  offerType: OfferType;
  qualifier: Qualifier;
  awardtype: AwardType;
  awardShort: ILocalizedObject;
  qualifierShort: ILocalizedObject;
  issuanceCode: string;
  mechanismInstructions: ILocalizedObject;
  mechanisms: IMechanismObject[];
  cashierInstructions?: ILocalizedObject;
  tiers: ITierObject[];
  displayPriority: number;
  regions: Region[];
  offerLimitation: OfferLimitation;
  offerLimitationText: ILocalizedObject;
  includedLocations: ILocalizedObject[];
  excludedLocation: ILocalizedObject[];
  includedBanners: ILocalizedObject[];
  excludedBanners: ILocalizedObject[];
  canBeCombined: boolean;
  combinationsText: ILocalizedObject;
  exclusions: ILocalizedObject;
  partnerUrl: ILocalizedObject;
  daysToApply: number;
  trademarkInfo: ILocalizedObject;
  description: ILocalizedObject;
  tags: string[];
  partnerLegalName: ILocalizedObject;
  hasCustomLegal: boolean;
  legalText: ILocalizedObject;
  print: boolean;
  image: ILocalizedObject<{ path: string }>;
  status?: OfferStatus;
  createdAt?: Date;
  createdBy?: string;
  campaignCode?: string;
  updatedAt?: Date;
  updatedBy?: string;
  publishedAt?: Date;
  publishedBy?: string;
}

export interface ILocalizedObject<T = string> {
  "en-US": T; // "en-US"
  "fr-CA": T; // "fr-CA"
}

export interface ITierObject {
  awardValue: number;
  qualifierValue: number;
  qualifierFrequency: number;
  content: ILocalizedObject[];
  qualifierLong: ILocalizedObject;
  awardLong: ILocalizedObject;
}

export interface IMechanismObject {
  mechanismType: MechanismType;
  mechanismLabel: ILocalizedObject;
  mechanismValue: string | ILocalizedObject;
  mechanismText: ILocalizedObject;
}
