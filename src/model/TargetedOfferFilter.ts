export interface ITargetedOfferFilters {
  CardNumber: string;
  Pagination: {
    PageNumber: string;
    RecordsPerPage: string;
    OrderBy: string;
  };
  Filters: {
    SUPPLIED_DATE: string;
    PARTNER_ID: string;
  };
}

export class TargetedOfferFilters implements ITargetedOfferFilters {
  public CardNumber: string;
  public Filters: {
    SUPPLIED_DATE: string;
    PARTNER_ID: string;
  };
  public Pagination = {
    PageNumber: "1",
    RecordsPerPage: "200",
    OrderBy: "PARTNER_ID"
  }

  constructor(collectorNumber: string, date: string, partnerId: string) {
    this.CardNumber = collectorNumber;
    this.Filters = {
      SUPPLIED_DATE: date,
      PARTNER_ID: partnerId
    };
  }
}
