import * as _ from "lodash";
import * as corsMiddleware from "restify-cors-middleware";
import * as healthRouterFactory from "restify-health-router";
import * as restify from "restify";

import Container, { Inject, Service } from "typedi";

import { BaseController } from "./controllers/BaseController";
import { GroupsController } from "./controllers/GroupsController";
import { OffersController } from "./controllers/OffersController";
import { PartnersController } from "./controllers/PartnersController";
import { REQUEST_HEADERS } from "./constants";
import RestifyFormatter from "./RestifyFormatter";
import { Router } from "restify-router";
import { UsersController } from "./controllers/UsersController";

@Service()
export class RestServer {
  private server: any;

  // TODO: look to fix this, it seems that without the initial injection, the Container won't be able to
  // Note: for all new controllers, they need to be injected here, otherwise TypeDI wont be able to pick it up for some reason
  @Inject()
  private groupsController: GroupsController;

  @Inject()
  private partnersController: PartnersController;

  @Inject()
  private usersController: UsersController;

  @Inject()
  private offersController: OffersController;

  constructor(config, controllers, log) {
    this.server = this._buildServer(config.get("name"), log);
    this._initializeServerErrorHandling();
    this._initializeRoutes(config, controllers);
  }

  public listen(port, callback) {
    this.server.listen(port, callback(this.server.name, this.server.url));
    this.server.log.info(`Listening on ${this.server.url}`);
  }

  public _buildServer(name, log) {
    const cors = corsMiddleware({
      allowHeaders: ["Authorization", REQUEST_HEADERS.USER_EMAIL],
      credentials: true,
      origins: ["http://localhost:8080", "http://localhost:3000"]
    });
    const server = restify.createServer({
      formatters: {
        "application/json": RestifyFormatter.jsonFormatter
      },
      log,
      name,
      handleUncaughtExceptions: true
    });

    // tslint:disable-next-line:no-console
    console.log("Initialize Server Middlewares");
    server.use(restify.plugins.requestLogger());

    // TODO re-enable when we auth against the server ourselves
    server.pre(cors.preflight);
    server.use(restify.plugins.queryParser({ mapParams: true }));
    server.use(restify.plugins.bodyParser({ mapParams: true }));
    server.use(cors.actual);

    server.on(
      "after",
      restify.plugins.auditLogger({
        log,
        event: "after",
        printLog: true,
        body: true
      })
    );
    server.on(
      "pre",
      restify.plugins.auditLogger({
        log,
        event: "pre",
        printLog: true,
        body: true
      })
    );

    return server;
  }

  public _initializeServerErrorHandling() {
    // tslint:disable-next-line:no-console
    console.log("Initialize Server Error Handling");
    // we mask our errors as some instance of a restifyError
    this.server.on("restifyError", (req, res, err, cb) => {
      req.log.error({ err, req }, "Restify Error occurred");
      return cb();
    });
  }

  public _initializeRoutes(config, controllers) {
    const router = new Router();
    _.forEach(controllers, (name: string) => {
      // tslint:disable-next-line:no-console
      console.log(`Binding Controller : ${name}`);
      (Container.get(name) as BaseController).initializeRestBindings(router);
    });
    router.applyRoutes(this.server);

    const healthRouter = healthRouterFactory.create({});
    // tslint:disable-next-line:no-console
    console.log("Binding: HealthRouter");
    healthRouter.applyRoutes(this.server);
  }
}
