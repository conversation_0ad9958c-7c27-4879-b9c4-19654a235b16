interface IApiErrorCodeMap {
  [key: string]: IApiErrorCode;
}

interface IApiErrorCode {
  code: keyof IApiErrorCodeMap;
  message: string;
}

export const API_ERROR_CODES: IApiErrorCodeMap = {
  VALIDATION_ERRORS: {
    code: "VALIDATION_ERRORS",
    message: "Errors while validating the inputs"
  },
  GROUP_NOT_FOUND: {
    code: "GROUP_NOT_FOUND",
    message: "Group not found"
  },
  GROUP_DUPLICATE_NAME: {
    code: "GROUP_DUPLICATE_NAME",
    message: "Group already exists"
  },
  GROUP_PARTNER_NOT_FOUND: {
    code: "GROUP_PARTNER_NOT_FOUND",
    message: "Role permission not found"
  },
  GROUP_PARTNER_DUPLICATE: {
    code: "GROUP_PARTNER_DUPLICATE",
    message: "Role permission already exists"
  },
  PARTNER_NOT_FOUND: {
    code: "PARTNER_NOT_FOUND",
    message: "Partner not found`"
  },
  PARTNER_DUPLICATE: {
    code: "PARTNER_DUPLICATE",
    message: "Partner already exists"
  },
  USER_GROUP_DUPLICATE: {
    code: "USER_GROUP_DUPLICATE",
    message: "Group already assigned to User"
  },
  USER_PARTNER_DUPLICATE: {
    code: "USER_PARTNER_DUPLICATE",
    message: "Partner already assigned to User"
  },
  USER_NOT_FOUND: {
    code: "USER_NOT_FOUND",
    message: "User not found"
  },
  BULK_JOB_NOT_FOUND: {
    code: "BULK_JOB_NOT_FOUND",
    message: "Bulk Job(s) with id(s) are not found"
  },
  BULK_ONLY_ONE_PARTNER: {
    code: "BULK_ONLY_ONE_PARTNER",
    message:
      "Bulk supports uploading and updating offers for only 1 partner at a time"
  },
  BULK_ATLEAST_ONE_OFFER: {
    code: "BULK_ATLEAST_ONE_OFFER",
    message: "Must include atleast 1 offer for upload or update"
  },
  MISSING_BULK_ID: {
    code: "MISSING_BULK_ID",
    message: "BULK ID is not specified"
  },
  PARTNER_NOT_ASSIGNED_TO_USER: {
    code: "PARTNER_NOT_ASSIGNED_TO_USER",
    message: "Partner is not assigned to user"
  },
  OFFER_IDS_REQUIRED: {
    code: "OFFER_IDS_REQUIRED",
    message: "At least one offer id is required"
  },
  OFFER_NOT_FOUND: {
    code: "OFFER_NOT_FOUND",
    message: "Offer(s) with id(s) are not found"
  },
  OFFER_REQUIRED: {
    code: "OFFER_REQUIRED",
    message: "Offer is required"
  },
  UNSPECIFIED_PARTNER: {
    code: "UNSPECIFIED_PARTNER",
    message: "PartnerId was not specified for the offer"
  },
  MISSING_OFFER_ID: {
    code: "MISSING_OFFER_ID",
    message: "Offer ID is not specified"
  },
  INTERNAL_ERROR: {
    code: "INTERNAL_ERROR",
    message: "Internal Error"
  }
};

export const REQUEST_HEADERS = {
  USER_EMAIL: "x-user-email"
};

export function apiErrorWrapper(
  apiErrorCode: IApiErrorCode,
  params?: any
): { info: IApiErrorCode } {
  return { info: { ...apiErrorCode, ...params } };
}

export enum ENV {
  LOCAL = "local",
  UAT = "uat",
  PROD = "production",
  DEVELOPMENT = "development",
  INT = "int"
}
