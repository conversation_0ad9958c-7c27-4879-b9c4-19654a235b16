import { MigrationInterface, QueryRunner } from "typeorm";

// Setup some default values for local development
const adminUsername = process.env.TYPEORM_ADMIN_USERNAME || "admin"
const adminPassword = process.env.TYPEORM_ADMIN_PASSWORD || "admin"
const username = process.env.TYPEORM_USERNAME || "user"
const userPassword = process.env.TYPEORM_PASSWORD || "user"

export class DatabaseUserAccounts1573224820956 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`START TRANSACTION;`);
    await queryRunner.query(`
      CREATE USER '${adminUsername}'@'%'
        IDENTIFIED BY '${adminPassword}';`);
    await queryRunner.query(`
      GRANT ALTER, CREATE, DELETE, DROP, INDEX, INSERT, SELECT, UPDATE
        ON *.*
        TO '${adminUsername}'@'%';`);
    await queryRunner.query(`
      CREATE USER '${username}'@'%'
        IDENTIFIED BY '${userPassword}';`);
    await queryRunner.query(`
      GRANT DELETE, INSERT, SELECT, UPDATE
        ON *.*
        TO '${username}'@'%';`);
    await queryRunner.query(`
      ALTER USER '${adminUsername}'@'%'
        REQUIRE SSL;`);
    await queryRunner.query(`COMMIT;`);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`DROP USER '${adminUsername}'@'%';`);
    await queryRunner.query(`DROP USER '${username}'@'%';`);
  }
}
