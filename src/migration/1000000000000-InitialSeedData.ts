import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSeedData1000000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
      INSERT INTO \`user\` 
      (id) 
      VALUES 
        ('<EMAIL>'),
        ('k<PERSON><EMAIL>'), 
        ('<EMAIL>'), 
        ('<EMAIL>'), 
        ('<EMAIL>'), 
        ('<PERSON><PERSON><PERSON><PERSON>@loyalty.com'), 
        ('<EMAIL>'), 
        ('<PERSON><PERSON><PERSON>@loyalty.com'),
        ('<EMAIL>'), 
        ('<PERSON><PERSON><PERSON><PERSON>@loyalty.com'),
        ('<EMAIL>')`);

    await queryRunner.query(`
      INSERT INTO \`groups\` 
      (id, name, version, is_editable, covers_all_partners) 
      VALUES
      (1, 'All Partners', 1, 0, 1),
      (200, 'Test Group 1', 1, 1, 0), 
      (201, 'Test Group 2', 1, 1, 0)`);

    await queryRunner.query(`
      INSERT INTO \`group_partner_mapping\` 
      (group_id, partner_Id) 
      VALUES 
      (200, 'b990f77a-6a27-47df-a373-8f227b32edc7'), 
      (201, '4a755252-876b-478e-9440-42961525e307')`);

    await queryRunner.query(`
      INSERT INTO \`user_partner_direct\` 
      (user_id, partner_Id) 
      VALUES
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>', '4a755252-876b-478e-9440-42961525e307'),
        ('<EMAIL>','4a755252-876b-478e-9440-42961525e307')`);

    await queryRunner.query(`
        INSERT INTO \`user_group\` 
        (user_id, group_id, created_ts) 
        VALUES 
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now()),
            ('<EMAIL>', 1, now())`);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query("DELETE FROM \`group_partner_mapping\`");
    await queryRunner.query("DELETE FROM \`user_group\`");
    await queryRunner.query("DELETE FROM \`groups\`");
    await queryRunner.query("DELETE FROM \`user_partner_direct\`");
    await queryRunner.query("DELETE FROM \`user\`");
  }
}
