import * as convict from "convict";
import * as path from "path";
import { Service } from "typedi";

@Service("Config")
export class Config {
  private configuration = null;

  constructor() {
    convict.addFormat({
      name: 'duration',
      validate: (val) => {
        if (!(typeof val === 'number' && val >= 0)) {
          throw new Error('must be a positive number');
        }
      },
      coerce: (val) => {
        // coerce string values to number if needed
        if (typeof val === 'string') {
          return Number(val);
        }
        return val;
      }
    });
    this.getConfiguration(process.env.NODE_ENV);
  }

  get config() {
    return this.configuration;
  }
  public getConfiguration(environmentOverride: any) {
    if (!this.configuration) {
      this.configuration = convict({
        env: {
          default: "local",
          doc: "The applicaton environment.",
          env: "NODE_ENV",
          format: ["local", "development", "int", "uat", "production"]
        },
        jsonClient: {
          connectTimeout: {
            arg: "connect-timeout",
            default: 20000,
            doc: "connect timeout for restify client",
            env: "CONNECT_TIMEOUT",
            format: "duration"
          },
          requestTimeout: {
            arg: "request-timeout",
            default: 15000,
            doc: "request timeout for restify client",
            env: "REQUEST_TIMEOUT",
            format: "duration"
          }
        },
        log: {
          stream: {
            enabled: {
              arg: "log-stream-enabled",
              default: true,
              doc: "Enable or disable the log",
              env: "LOG_STREAM_ENABLED",
              format: Boolean
            },
            level: {
              arg: "log-stream-level",
              default: "info",
              doc: "The minimum log level that will be written to a file",
              env: "LOG_STREAM_LEVEL",
              format: ["trace", "debug", "info", "warn", "error", "fatal"]
            }
          }
        },
        name: {
          default: "post-security-manager",
          doc: "The name of the server.",
          env: "APP_NAME",
          format: String
        },
        port: {
          arg: "port",
          default: 8081,
          doc: "The port to bind.",
          env: "PORT",
          format: "port"
        },
        partnersEndpoint: {
          default: "",
          doc: "Endpoint for the Partners API",
          env: "PARTNERS_API",
          format: String
        },
        offersEndpoint: {
          default: "",
          doc: "Endpoint for the Offers POST API",
          env: "POST_API",
          format: String
        },
        useMockOffers: {
          default: false,
          doc: "Flag to indicate to use mock offers",
          env: "USE_MOCK_OFFERS",
          format: Boolean
        },
        useMockContent: {
          default: false,
          doc: "Flag used to indicate if we mock data from the content service",
          env: "USE_MOCK_CONTENT",
          format: Boolean
        },
        contentEndpoint: {
          default: "",
          doc: "Endpoint used to generate copytext for offers",
          env: "CONTENT_ENDPOINT",
          format: String
        }
      });

      // Normal settings are taken from the .json files in the config folder, but secret properties are exported as environment variables from the env files

      // tslint:disable:no-unused-expression
      environmentOverride
        ? this.configuration.set("env", environmentOverride)
        : void 0;
      const env = this.configuration.get("env");
      try {
        this.configuration.loadFile(path.join(__dirname, `/${env}.json`));
        // tslint:disable-next-line:no-console
        console.log(
          `Successfully loaded the environment configuration for ${env}`,
          this.configuration._instance
        );
      } catch (err) {
        // tslint:disable-next-line:no-console
        console.error(
          `Failed to load environment dependent configuration for '${env}' with error: ${err}`
        );
      }

      this.configuration.validate();
    }
    return this.configuration;
  }
}
