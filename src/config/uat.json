{"env": "uat", "partnersEndpoint": "https://partners.uat.api.loyalty.com:2223/partners/v1/partners", "offersEndpoint": "https://offer-management.uat.api.loyalty.com:2024/offer-management-api/v3", "contentEndpoint": "https://content-generation.uat.api.loyalty.com:2023/content-generation-service/v1", "agilityEndpoint": "https://u2lone-pveapi.epsilonagilityloyalty.com", "agilityCredentialSecrets": "uat-nova-agility-vault-credentials", "agilityTokenSecrets": "uat-nova-agility-vault-authorization-token", "agilityProgramCode": "AMRP", "offerDeliveryEndpoint": "https://apigwproxy-ca.uat.api.loyalty.com:34025", "useMockOffers": false, "useMockBulkJobs": false, "useMockContent": false, "useMockCategories": false, "useMockPromotions": false, "log": {"stream": {"enabled": true, "level": "debug"}}}