{"env": "development", "partnersEndpoint": "mockdata/partners.json", "offersEndpoint": "https://offer-management.dev.api.loyalty.com:2024/offer-management-api/v3", "contentEndpoint": "https://content-generation.dev.api.loyalty.com:2023/content-generation-service/v1", "agilityEndpoint": "https://u2lone-pveapi.epsilonagilityloyalty.com", "agilityCredentialSecrets": "dev-nova-agility-vault-credentials", "agilityTokenSecrets": "dev-nova-agility-vault-authorization-token", "agilityProgramCode": "AMRPDEV", "offerDeliveryEndpoint": "https://apigwproxy-ca.dev.api.loyalty.com:34025", "useMockOffers": false, "useMockBulkJobs": false, "useMockContent": false, "useMockCategories": false, "useMockPromotions": false, "log": {"stream": {"enabled": true, "level": "debug"}}}