{"env": "local", "partnersEndpoint": "mockdata/partners.json", "offersEndpoint": "http://localhost:8080/offer-management-api/v3", "contentEndpoint": "http://localhost:9000", "agilityEndpoint": "https://u2lone-pveapi.epsilonagilityloyalty.com", "agilityCredentialSecrets": "dev-nova-agility-vault-credentials", "agilityTokenSecrets": "dev-nova-agility-vault-authorization-token", "agilityProgramCode": "AMRPDEV", "offerDeliveryEndpoint": "test", "useMockOffers": true, "useMockBulkJobs": true, "useMockContent": true, "useMockCategories": true, "useMockPromotions": true, "port": 8082, "log": {"stream": {"enabled": true, "level": "debug"}}}