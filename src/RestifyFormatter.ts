import restifyErrors = require("restify-errors");
import verror = require("verror");
class RestifyFormatter {
  public static jsonFormatter(req, res, body, cb) {
    try {
      let formattedBody = null;
  
      if (body instanceof Error) {
        formattedBody = RestifyFormatter._formatError(body, req);
      } else {
        formattedBody = JSON.stringify(body);
      }
  
      res.setHeader("Content-Length", Buffer.byteLength(formattedBody));
      return formattedBody;
    }
    catch(exception) {
      // tslint:disable-next-line:no-console
      console.error(exception);
    }
  }

  public static _formatError(error, req) {
    try {
      // overwrite the built-in toJSON
      (error as restifyErrors.RestError).toJSON = function() {
        const self = this;
  
        return {
          ...verror.info(self)
        };
      };
      return JSON.stringify(error);
    }
    catch(exception) {
      // tslint:disable-next-line:no-console
      console.error(exception);
    }
  }
}

export default RestifyFormatter;
