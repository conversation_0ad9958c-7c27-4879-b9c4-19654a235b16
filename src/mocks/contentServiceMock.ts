import nock = require("nock");

import { Config } from "../config/config";
import Container from "typedi";

import _ = require("lodash");

export function createContentServiceMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("contentEndpoint");
  generateContentMocks(baseURL);
}

function generateContentMocks(baseURL: string) {
  nock(baseURL, { allowUnmocked: true})
    .post("/generate-content")
    .reply(200, require("../mockdata/sample_copy_content.json"))
    .persist();
}

