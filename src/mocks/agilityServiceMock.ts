import nock = require("nock");

import { Config } from "../config/config";
import Container from "typedi";

export function createAgilityServiceMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("agilityEndpoint");
  generateAgilityMocks(baseURL);
}

function generateAgilityMocks(baseURL: string) {
  // `query(true)` is specified as "req.params" that are being passed blindly to query params contains
  // body of the request as well

  nock(baseURL, { allowUnmocked: true })
    .post("/api/v1/infrastructure/scripts/GetProfileProgramOffers/invoke")
    .query(true)
    .reply(200, { Response: [{ SysOfferName: "test-guid-id" }] })
    .persist();
}
