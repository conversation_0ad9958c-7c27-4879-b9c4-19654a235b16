import nock = require("nock");

import { Config } from "../config/config";
import Container from "typedi";

export function createAgilityCredentialsServiceMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("agilityEndpoint");
  generateAgilityCredentialsMocks(baseURL);
}

function generateAgilityCredentialsMocks(baseURL: string) {
  // `query(true)` is specified as "req.params" that are being passed blindly to query params contains
  // body of the request as well

  nock(baseURL, { allowUnmocked: true })
    .post("/api/v1/authorization/tokens")
    .query(true)
    .reply(201, {
      "Username": "AIRMILES_SYSTEM_UAT",
      "AccessToken": "XXXX",
      "RefreshToken": "XXXX",
      "AccessTokenExpiration": "2030-11-27T03:16:23.882548Z",
      "RefreshTokenExpiration": "2030-11-27T09:56:23.882548Z",
      "Success": true,
      "RequireSsl": true,
      "IsPasswordExpired": true,
      "TenantId": "XXX",
      "TenantName": "FUSION TENANT"
    })
    .persist();
}
