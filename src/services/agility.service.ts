import { Service } from "typedi";
import { Config } from "../config/config";
import { AgilityCredentialsService } from "./agilityCredentials.service";
import request = require("request-promise");
import Errors = require("restify-errors");
import { apiErrorWrapper } from "../constants";
import { ITargetedOfferFilters } from "../model/TargetedOfferFilter";
import { createAgilityServiceMock } from "../mocks/agilityServiceMock";

@Service("AgilityService")
export class AgilityService {
  constructor(private config: Config, private credentialsService: AgilityCredentialsService) {
    if (this.config.config.get("useMockOffers")) {
      // tslint:disable-next-line: no-console
      createAgilityServiceMock();
    }
  }

  public async getTargetedOffers(
    filters: ITargetedOfferFilters,
  ): Promise<any> {
    const accessToken = await this.credentialsService.getToken();
    const headers = {
      "Content-Type": "application/json",
      "Accept-Language": "en-US",
      "Program-Code": this.config.config.get("agilityProgramCode"),
      "Authorization": `OAuth ${accessToken}`
    }

    return await this.makeEndpointCall(
      "/api/v1/infrastructure/scripts/GetProfileProgramOffers/invoke",
      "POST",
      filters,
      headers
    );
  }

  private async makeEndpointCall(
    path: string,
    method: string,
    body: any,
    additionalHeaders?: { [s: string]: string }
  ) {
    try {
      const requestParams = {
        uri: `${this.config.config.get("agilityEndpoint")}${path}`,
        headers: additionalHeaders,
        body,
        method,
        json: true,
      };

      // tslint:disable-next-line:no-console
      console.debug(
        "Making a request with params " + JSON.stringify(requestParams)
      );

      const response = await request(requestParams);
      // tslint:disable-next-line:no-console
      console.debug("Response: " + JSON.stringify(response));
      return response;
    } catch (e) {
      this.throwRestifyError(e);
    }
  }

  private throwRestifyError(e) {
    // tslint:disable-next-line:no-console
    console.error(e);
    const error = apiErrorWrapper(e.error);
    switch (e.statusCode) {
      case 400:
        throw new Errors.BadRequestError(error);
      case 503:
        throw new Errors.ServiceUnavailableError(error);
      case 504:
        throw new Errors.GatewayTimeoutError(error);
      default:
        throw new Errors.InternalServerError(error);
    }
  }
}
