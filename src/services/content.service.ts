import { Config } from "../config/config";
import { ICopyText } from "../model/Offer";
import { Service } from "typedi";
import { apiErrorWrapper, ENV } from "../constants";

import request = require("request-promise");

import Errors = require("restify-errors");
import { createContentServiceMock } from "../mocks/contentServiceMock";

export const CONTENT_API = {
  GENERATE_CONTENT: "/generate-content"
};

@Service("ContentService")
export class ContentService {
  constructor(private config: Config) {
    if (this.config.config.get("useMockContent")) {
      createContentServiceMock();
    }
  }


  /**
   * Sends a request to the generate-content service to create the copy text content for offers
   * Returns the associated copy text
   * @param req 
   */
  public async generateCopyTextContent(req: any): Promise<ICopyText> {
    req.method = 'post';
    return await this.makeEndpointCall(CONTENT_API.GENERATE_CONTENT, req);
  }

  /**
   * Allows us to call mock or actual endpoint
   * @param path the path on the offers endpoint to call
   */
  private async makeEndpointCall(path: string, req: any) {
    try {
      return await request({
        uri: `${this.config.config.get("contentEndpoint")}${path}`,
        body: req.body,
        method: req.method,
        json: true,
      });
    } catch (e) {
      this.throwRestifyError(e);
    }
  }

  private throwRestifyError(e) {
    // tslint:disable-next-line:no-console
    console.error(e);
    const error = apiErrorWrapper(e.error);
    switch (e.statusCode) {
      case 400:
        throw new Errors.BadRequestError(error);
      default:
        throw new Errors.InternalServerError(error);
    }
  }
}
