import { Repository } from "typeorm";
import { Service, Inject } from "typedi";
import Errors = require("restify-errors");
import { ORM_ERRORS } from "../errors/orm.errors";
import { getRepositoryFromContainer } from "../utils/repository.utils";
import { apiErrorWrapper, API_ERROR_CODES } from "../constants";
import { User } from "../entity/User";
import { UserGroup } from "../entity/UserGroup";
import { UserPartnerDirect } from "../entity/UserPartnerDirect";
import { validate } from "class-validator";
import { Config } from "../config/config";
import { Partner } from "../model/Partner";
import { GroupsService } from "./groups.service";
import { Group } from "../entity/Group";
import { GroupPartnerMapping } from "../entity/GroupPartnerMapping";
import { PartnersService } from "./partners.service";

// tslint:disable-next-line
const request = require("request-promise");

@Service("UsersService")
export class UsersService {
  @Inject("GroupsService")
  private groupsService: GroupsService;
  @Inject("PartnersService")
  private partnersService: PartnersService;

  private userRepository: Repository<User>;
  private userGroupRepository: Repository<UserGroup>;
  private userPartnerDirectRepository: Repository<UserPartnerDirect>;

  constructor(private config: Config) {
    this.userRepository = getRepositoryFromContainer("UserRepository", User);
    this.userGroupRepository = getRepositoryFromContainer(
      "UserGroupRepository",
      UserGroup
    );
    this.userPartnerDirectRepository = getRepositoryFromContainer(
      "UserPartnerDirectRepository",
      UserPartnerDirect
    );
  }

  public async getUserById(userId: string): Promise<User> {
    return await this.userRepository.findOne(userId, {
      relations: ["partners"]
    });
  }

  public async addGroupToUser(
    userId: string,
    groupId: number,
    requestingUserId: string
  ): Promise<UserGroup> {
    const userGroup: UserGroup = this.userGroupRepository.create();
    userGroup.userId = userId;
    userGroup.groupId = groupId;
    userGroup.createdBy = requestingUserId;
    try {
      return await this.userGroupRepository.save(userGroup);
    } catch (error) {
      if (error.code === ORM_ERRORS.DUPLICATE_ENTRY) {
        throw new Errors.ConflictError(
          apiErrorWrapper(API_ERROR_CODES.USER_GROUP_DUPLICATE)
        );
      }
      throw new Errors.InternalError(error);
    }
  }

  public async addPartnerToUser(
    userId: string,
    partnerId: string,
    requestingUserId: string
  ): Promise<UserPartnerDirect> {
    const partner: UserPartnerDirect = this.userPartnerDirectRepository.create();
    partner.userId = userId;
    partner.partnerId = partnerId;
    partner.createdBy = requestingUserId;
    try {
      return await this.userPartnerDirectRepository.save(partner);
    } catch (error) {
      if (error.code === ORM_ERRORS.DUPLICATE_ENTRY) {
        throw new Errors.ConflictError(
          apiErrorWrapper(API_ERROR_CODES.USER_PARTNER_DUPLICATE)
        );
      }
      throw new Errors.InternalError(error);
    }
  }

  public async doesUserExist(userId: string): Promise<User> {
    if (!userId) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
      );
    }
    const user = await this.getUserById(userId);
    if (!user) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
      );
    }
    return user;
  }

  public async removeGroupFromUser(userId: string, groupId: number) {
    await this.userGroupRepository.delete({ userId, groupId });
    return Promise.resolve();
  }

  public async removePartnerFromUser(userId: string, partnerId: string) {
    await this.userPartnerDirectRepository.delete({ partnerId, userId });
    return Promise.resolve();
  }

  public async addUser(user: User): Promise<User> {
    const validationErrors = await validate(user);
    if (validationErrors.length > 0) {
      throw new Errors.InvalidContentError(
        apiErrorWrapper(API_ERROR_CODES.VALIDATION_ERRORS, { validationErrors })
      );
    } else {
      try {
        return await this.userRepository.save(user);
      } catch (error) {
        if (error.code === ORM_ERRORS.DUPLICATE_ENTRY) {
          throw new Errors.ConflictError(
            apiErrorWrapper(API_ERROR_CODES.USER_DUPLICATE_ID)
          );
        }
        throw new Errors.InternalError(error);
      }
    }
  }

  public async getPartnersForUser(
    userId: string,
    includeGroupPartners: boolean = false
  ): Promise<Partner[]> {
    const user = await this.getUserById(userId);
    const partnerIds: string[] = user.partners
      ? user.partners.map((partner: UserPartnerDirect) => partner.partnerId)
      : [];
    let shouldReturnAllPartners: boolean = false;

    if (includeGroupPartners) {
      const groups = await this.groupsService.getGroupsByUser(userId);
      groups.forEach(async (group: Group) => {
        // If the user belongs to any group with AllPartners shortcircuit and return all of the partners
        if (group.coversAllPartners) {
          shouldReturnAllPartners = true;
          return;
        }
        group.partnerMappings.forEach((partner: GroupPartnerMapping) => {
          if (partnerIds.indexOf(partner.partnerId) < 0) {
            partnerIds.push(partner.partnerId);
          }
        });
      });
    }
    if (shouldReturnAllPartners) {
      return await this.partnersService.getAllPartners();
    }

    return partnerIds.length === 0
      ? []
      : await this.partnersService.getPartnersByIds(partnerIds);
  }
}
