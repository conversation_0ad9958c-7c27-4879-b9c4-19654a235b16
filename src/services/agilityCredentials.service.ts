import { SecretsManager } from "aws-sdk";
import Container, { Service } from "typedi";
import { Config } from "../config/config";
import moment = require("moment");
import request = require("request-promise");
import { createAgilityCredentialsServiceMock } from "../mocks/agilityCredentialsServiceMock";

export interface IAgilityCredentials {
  username: string;
  password: string;
  authorization_token: string;
}

export interface ITokenDetails {
  Username?: string;
  AccessToken: string;
  RefreshToken: string;
  AccessTokenExpiration: string;
  RefreshTokenExpiration: string;
  Success?: boolean;
  RequireSsl?: boolean;
  IsPasswordExpired?: boolean;
  TenantId?: string;
  TenantName?: string;
}

@Service("SecretsService")
export class AgilityCredentialsService {
  private secretsManager: SecretsManager;
  private credentials: IAgilityCredentials;
  private tokenDetails: ITokenDetails = null;

  constructor(private config: Config) {
    if (this.config.config.get("useMockOffers")) {
      createAgilityCredentialsServiceMock();
    }
    this.secretsManager = new SecretsManager({ region: "us-east-1" }); // TODO: Exclude region?
    this.config = Container.get("Config");
  }

  public async getToken(): Promise<string> {
    const currentDate = moment();
    // check if we have a valid token
    if (
      !this.tokenDetails ||
      currentDate.isAfter(moment(this.tokenDetails.RefreshTokenExpiration))
    ) {
      // we have no token, or we can't use our refresh
      // tslint:disable-next-line: no-console
      console.log("Getting new token");
      this.tokenDetails = await this.fetchToken();
    } else {
      if (
        currentDate.isAfter(moment(this.tokenDetails.AccessTokenExpiration)) // Check if we need to refresh
      ) {
        // tslint:disable-next-line: no-console
        console.log("Fetching refresh token");
        this.tokenDetails = await this.fetchToken(
          this.tokenDetails.RefreshToken
        );
      }
    }

    return this.tokenDetails.AccessToken;
  }

  private async fetchToken(refreshToken?: string) {
    const credentials = await this.getCredentials();
    const requestMethod = refreshToken ? "PUT" : "POST";

    let requestBody: any = `grant_type=password&username=${credentials.username}&password=${credentials.password}&response_type=token`;

    if (refreshToken) {
      requestBody = `grant_type=refresh_token&refresh_token=${refreshToken}`;
    }

    const requestParams = {
      uri: `${this.config.config.get(
        "agilityEndpoint"
      )}/api/v1/authorization/tokens`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept-Language": "en-US",
        Authorization: `Basic ${credentials.authorization_token}`
      },
      body: requestBody,
      method: requestMethod,
      json: true
    };

    // tslint:disable-next-line: no-console
    console.log(
      "Making a request with params",
      JSON.stringify(requestParams).replace(
        RegExp(
          `${credentials.username}|${credentials.password}|${credentials.authorization_token}`,
          "g"
        ),
        "******"
      )
    );

    const response = await request(requestParams);

    return response;
  }

  private async getCredentials(): Promise<IAgilityCredentials> {
    if (this.config.config.get("useMockOffers")) {
      return {
        username: "mockuser",
        password: "mockpw",
        authorization_token: "mocktoken"
      };
    }

    if (this.credentials) {
      return this.credentials;
    }

    const credentialsSecret = await this.secretsManager
      .getSecretValue({
        SecretId: this.config.config.get("agilityCredentialSecrets")
      })
      .promise();
    const tokenSecret = await this.secretsManager
      .getSecretValue({
        SecretId: this.config.config.get("agilityTokenSecrets")
      })
      .promise();

    const parsedCredentials = {
      ...JSON.parse(credentialsSecret.SecretString),
      ...JSON.parse(tokenSecret.SecretString)
    };

    this.credentials = parsedCredentials;

    return parsedCredentials;
  }
}
