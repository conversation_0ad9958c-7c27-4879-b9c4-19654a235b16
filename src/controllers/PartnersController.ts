import { BaseController } from "./BaseController";
import { PartnersService } from "../services/partners.service";
import { Service } from "typedi";

@Service("PartnersController")
export class PartnersController extends BaseController {
  constructor(private partnersService: PartnersService) {
    // call base constructor
    super();
    // create bindings list
    this.bindings = [
      {
        handler: this.onGetPartners,
        method: "get",
        name: "Get all partners",
        path: "/partners"
      }
    ];
  }

  /**
   * @api {post} /partners/ Get all Partners
   * @apiName onGetPartners
   * @apiGroup Partner
   *
   * @apiSuccess Array of all partners
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *      [{
   *          "name": "Rexall",
   *           "sponsorCodes": ["RXAL"],
   *           "type": ["in-store", "cash", "load+go"],
   *          "fullLogo": [{
   *            "title": "@2x",
   *            "file": {
   *              "url": "//images.ctfassets.net/hpbflulg5svm/MRgY4ZtEcgc6iy0M6GEkO/739b6e7f59a6e395511e93964c1c1032/Rexall_Drugstore.png",
   *              "details": {
   *                "size": 18339,
   *                "image": {
   *                  "width": 512,
   *                 "height": 512
   *               }
   *             },
   *             "fileName": "Rexall Drugstore.png",
   *             "contentType": "image/png"
   *            }
   *         }],
   *         "baseEarnRate": "1 Mile for every $20 spent per transaction",
   *          "maxCashMiles": "Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day.",
   *          "regions": ["ab", "bc", "mb", "nt", "on", "sk"],
   *      }]
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async onGetPartners(req, res, next) {
    res.send(await this.partnersService.getAllPartners());
    next();
  }
}
