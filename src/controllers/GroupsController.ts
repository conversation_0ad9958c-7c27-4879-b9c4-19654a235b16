import { GroupsService } from "../services/groups.service";
import { BaseController } from "./BaseController";
import { Group } from "../entity/Group";
import { GroupPartnerMapping } from "../entity/GroupPartnerMapping";
import { Partner } from "../model/Partner";
import { PartnersService } from "../services/partners.service";
import { Service } from "typedi";
import { UserGroup } from "../entity/UserGroup";
import {
  REQUEST_HEADERS,
  apiErrorWrapper,
  API_ERROR_CODES
} from "../constants";
import { User } from "../entity/User";
import { UsersService } from "../services/users.service";
import Errors = require("restify-errors");

@Service("GroupsController")
export class GroupsController extends BaseController {
  constructor(
    private groupsService: GroupsService,
    private partnersService: PartnersService,
    private usersService: UsersService
  ) {
    // call base constructor
    super();
    // create bindings list
    this.bindings = [
      {
        handler: this.onGetGroups,
        method: "get",
        name: "Get groups",
        path: "/groups"
      },
      {
        handler: this.onAddGroup,
        method: "post",
        name: "add group",
        path: "/groups"
      },
      {
        handler: this.getGroupById,
        method: "get",
        name: "Get Group by ID",
        path: "/groups/:id"
      },
      {
        handler: this.addPartnerToGroup,
        method: "post",
        name: "Add Partner to Group",
        path: "/groups/:groupId/partners"
      },
      {
        handler: this.deletePartnerFromGroup,
        method: "del",
        name: "Delete Partner from Group",
        path: "/groups/:groupId/partners/:partnerId"
      },
      {
        handler: this.getUsersByGroupId,
        method: "get",
        name: "Get users for Group",
        path: "/groups/:groupId/users"
      }
    ];
  }

  /**
   * @api {get} /groups/ Get all Groups and associated Partners in groups
   * @apiName GetGroups
   * @apiGroup Group
   *
   * @apiSuccess {object} result object containing array of groups and partners
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *     {
   *       "groups": [],
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some Message",
   *     }
   */
  public async onGetGroups(req, res, next) {
    let groups: Group[] = await this.groupsService.getAllGroups();
    groups = await this.groupsService.getGroupsWithPartnerDetails(groups);

    res.send(groups);
    next();
  }

  /**
   * @api {post} /groups/ Add a new Group
   * @apiName AddGroup
   * @apiGroup Group
   *
   * @apiSuccess {object} result object containing group object
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       {
   *           "id": 200,
   *           "name": "Test",
   *           "coversAllPartners": false,
   *           "partnerMappings": [
   *               {
   *                   "groupId": 200,
   *                   "partnerId": "b990f77a-6a27-47df-a373-8f227b32edc7"
   *               }
   *           ],
   *           "partners": []
   *       }
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async onAddGroup(req, res, next) {
    const requestingUserId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    await this.getUserFromEmail(requestingUserId);

    res.send(await this.groupsService.addNewGroup(req.body, requestingUserId));
    next();
  }

  /**
   * @api {post} /groups/:id Get Group by Id
   * @apiName GetGroupById
   * @apiGroup Group
   *
   * @apiSuccess {Group} result object containing group object
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       {
   *           "id": 200,
   *           "name": "Test",
   *           "coversAllPartners": false,
   *           "partnerMappings": [
   *               {
   *                   "groupId": 200,
   *                   "partnerId": "b990f77a-6a27-47df-a373-8f227b32edc7"
   *               }
   *           ],
   *           "partners": []
   *       }
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "An Error Occurred",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Group ID cannot be empty",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Group with ID does not exist",
   *     }
   */
  public async getGroupById(req, res, next) {
    const groupId: number = req.params.id;
    const group = await this.groupsService.doesGroupExist(groupId);
    group.partners = await this.getPartnersForGroupId(group);

    res.send(group);
    next();
  }

  /**
   * @api {post} /groups/:groupId/partners Add Partner to Group by ID
   * @apiName addPartnerToGroup
   * @apiGroup Group
   *
   * @apiSuccess {Group} result object containing group object
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       {
   *           "id": 200,
   *           "name": "Test",
   *           "coversAllPartners": false,
   *           "partnerMappings": [
   *               {
   *                   "groupId": 200,
   *                   "partnerId": "b990f77a-6a27-47df-a373-8f227b32edc7"
   *               }
   *           ],
   *           "partners": []
   *       }
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "An Error Occurred",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Group ID cannot be empty",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Group with ID does not exist",
   *     }
   */
  public async addPartnerToGroup(req, res, next) {
    const requestingUserId: string = req.header(REQUEST_HEADERS.USER_EMAIL);
    await this.getUserFromEmail(requestingUserId);

    const groupId: number = req.params.groupId;
    const partnerId: string = req.body.partnerId;
    await this.groupsService.doesGroupExist(groupId);
    await this.partnersService.doesPartnerExist(partnerId);
    await this.groupsService.addPartnerToGroup(
      groupId,
      partnerId,
      requestingUserId
    );
    const group = await this.groupsService.getGroupById(groupId);

    group.partners = await this.getPartnersForGroupId(group);

    res.send(group);
    next();
  }

  /**
   * @api {delete} /groups/:groupId/partners/:partnerId Remove Partner from Group by ID
   * @apiName deletePartnerFromGroup
   * @apiGroup Group
   *
   * @apiSuccess empty response
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "An Error Occurred",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Group ID cannot be empty",
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Group with ID does not exist",
   *     }
   */
  public async deletePartnerFromGroup(req, res, next) {
    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);
    await this.getUserFromEmail(userId);

    const groupId: number = req.params.groupId;
    const partnerId: string = req.params.partnerId;
    await this.groupsService.doesGroupExist(groupId);
    await this.partnersService.doesPartnerExist(partnerId);

    await this.groupsService.deletePartnerFromGroup(groupId, partnerId);
    const group = await this.groupsService.getGroupById(groupId);

    group.partners = await this.getPartnersForGroupId(group);

    res.send(group);
    next();
  }

  /**
   * @api {get} /groups/:groupId/users Get users by group id
   * @apiName getUsersByGroupId
   * @apiGroup Group
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *
   * [
   * {
   *     "userId": "<EMAIL>",
   *     "groupId": 200,
   *     "createdDate": null
   *  }
   * ]
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request
   *     {
   *          "code": "GROUP_NOT_FOUND",
   *          "message": "Group not found`"
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "GROUP_NOT_FOUND",
   *          "message": "Group not found`"
   *     }
   */
  public async getUsersByGroupId(req, res, next) {
    const groupId: number = req.params.groupId;
    await this.groupsService.doesGroupExist(groupId);
    const users: UserGroup[] = await this.groupsService.getUsersByGroupId(
      groupId
    );
    res.send(users);
    return next();
  }

  private async getPartnersForGroupId(group: Group): Promise<Partner[]> {
    const partnerIds: string[] = group.partnerMappings
      ? group.partnerMappings.map(
          (partner: GroupPartnerMapping) => partner.partnerId
        )
      : [];

    if (partnerIds.length) {
      return await this.partnersService.getPartnersByIds(partnerIds);
    } else {
      return Promise.resolve([]);
    }
  }

  private async getUserFromEmail(email: string): Promise<User> {
    const user: User = await this.usersService.doesUserExist(email);

    if (!user) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
      );
    }
    return user;
  }
}
