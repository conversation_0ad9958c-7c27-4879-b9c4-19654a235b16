FROM 277983268692.dkr.ecr.us-east-1.amazonaws.com/node:22-alpine

COPY jenkins/kms-env.tgz .
RUN npm i -g kms-env.tgz
RUN rm kms-env.tgz

COPY jenkins/env-decrypt /usr/local/bin/

WORKDIR /opt/app

# Copy contents of build to /opt/app
ADD dist /opt/app
# Give ownership to daemon user
RUN ["chown", "-R", "daemon:daemon", "."]
USER daemon

EXPOSE 8081
# Run env-decrypt followed by npm start
# allow any commands to supply arguments to the node app
ENTRYPOINT ["/usr/local/bin/env-decrypt", "yarn", "start"]