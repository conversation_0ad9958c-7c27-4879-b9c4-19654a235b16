{"ClusterStackName": "prod-cpu-amrpwl-ecs", "AppName": "post-security-manager", "AppDesiredCount": "1", "AppMaxCount": "7", "AppMinCount": "1", "AutoScaleHighThreshold": "70", "AutoScaleLowThreshold": "40", "AppContainerPort": "8081", "AppCpu": "256", "AppMemory": "512", "ContainerTotalCpu": "512", "ContainerTotalMemory": "2048", "KmsKey": "arn:aws:kms:us-east-1:559165213921:key/64a550a1-56b2-4843-8b85-59a5d13e21a9", "Environment": "prod", "EnvironmentType": "prod", "PagerDutyURL": "https://events.pagerduty.com/integration/9a2fd325f6ef4fc181ba956fbb5c2f65/enqueue", "NetworkStackName": "AMRPWL-Prod", "AgilityCredentialsSecretId": "prod-nova-agility-vault-credentials", "AgilityTokenSecretId": "prod-nova-agility-vault-authorization-token", "KinesisForSplunkStackName": "prod-kinesissplunk", "MemoryReservationThreshold": "80", "HealthyHostThreshold": "1", "CPUHighThresholdPagerDuty": "90"}