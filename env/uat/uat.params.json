{"ClusterStackName": "uat-cpu-amrpwl-ecs", "AppName": "post-security-manager", "AppDesiredCount": "1", "AppMaxCount": "7", "AppMinCount": "1", "AutoScaleHighThreshold": "70", "AutoScaleLowThreshold": "40", "AppContainerPort": "8081", "AppCpu": "256", "AppMemory": "512", "ContainerTotalCpu": "512", "ContainerTotalMemory": "2048", "KmsKey": "arn:aws:kms:us-east-1:022122625207:key/2d34a357-269d-4e30-b301-efb84eefe2c4", "Environment": "uat", "EnvironmentType": "nonprod", "PagerDutyURL": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue", "NetworkStackName": "AMRPWL-UAT", "AgilityCredentialsSecretId": "uat-nova-agility-vault-credentials", "AgilityTokenSecretId": "uat-nova-agility-vault-authorization-token", "KinesisForSplunkStackName": "nonprod-kinesissplunk", "MemoryReservationThreshold": "80", "HealthyHostThreshold": "1", "CPUHighThresholdPagerDuty": "90"}