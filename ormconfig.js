const fs = require("fs");

const baseConfig = {
   type: "mysql",
   driver: "mysql2",
   host: process.env.TYPEORM_HOST || "127.0.0.1",
   port: process.env.TYPEORM_PORT || 3308,
   database: process.env.TYPEORM_DATABASE || "postsecurity",
   synchronize: process.env.TYPEORM_SYNCHRONIZE || false,
   ssl : process.env.TYPEORM_SSL_AMAZON_RDS === "true" ? {
     ca: fs.readFileSync("/usr/share/ca-certificates/global-bundle.pem")
   } : false,
   logging: process.env.TYPEORM_LOGGING || ["query", "error", "schema", "warn"],
   logger: "simple-console",
   columnHint: "snakeCase",
   charset: "utf8mb4_unicode_ci",
   entities: [
      "build/src/entity/*.js"
   ], 
   migrations: [
      "build/src/migration/*.js"
   ],
   subscribers: [
      "build/src/subscriber/*.js"
   ],
   cli: {
      "entitiesDir": "src/entity",
      "migrationsDir": "src/migration",
      "subscribersDir": "src/subscriber"
   }
};

const masterConfig = {
   name: "master",
   username: process.env.TYPEORM_MASTER_USERNAME || "root",
   password: process.env.TYPEORM_MASTER_PASSWORD || "root",
   migrationsRun: true
};

const adminConfig = {
   name: "admin",
   username: process.env.TYPEORM_ADMIN_USERNAME || "admin",
   password: process.env.TYPEORM_ADMIN_PASSWORD || "admin"
};

const userConfig = {
   name: "user",
   username: process.env.TYPEORM_USERNAME || "user",
   password: process.env.TYPEORM_PASSWORD || "user"
};

module.exports = [
   { ...baseConfig, ...masterConfig },
   { ...baseConfig, ...adminConfig },
   { ...baseConfig, ...userConfig }
];