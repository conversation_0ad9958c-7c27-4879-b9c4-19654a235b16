#!/bin/bash
# Create SSH Tunnels to access AWS Resources sitting on AWS private Network through the Bastion server

# PEM Key to Access Bastion
PEM_KEY=
PATH_TO_PEM=
# Bastion Information
BASTION_USER=ec2-user
BASTION_HOST=

# Port to forward DB calls to locally
LOCAL_RBAC_DB_PORT=3308
LOCAL_SECURITY_DB_PORT=3307
AWS_DB_PORT=3306

# Port to forward API calls to locally
LOCAL_RBAC_API_PORT=8081
LOCAL_SECURITY_API_PORT=8082
AWS_API_PORT=443

setup_ssh_urls() {
  read -p "Choose your env (dev, uat, prod | Default is dev): " CHOSEN_ENV
  if [[ -z $CHOSEN_ENV ]]; then
    echo "Using dev..."
    CHOSEN_ENV="dev"
  fi

  echo "Setting up SSH tunnels to $CHOSEN_ENV..."
  if [[ $CHOSEN_ENV == "dev" ]]; then
    BASTION_HOST=***********
    PEM_KEY=dev-us-east-1-root-kp.pem
    # RDS Endpoints
    RBAC_RDS_CLUSTER_ENDPOINT=dev-offers-rbac-aurora-rds-databasecluster-10hcv194bl82v.cluster-cotm4atjnnku.us-east-1.rds.amazonaws.com
    SECURITY_RDS_CLUSTER_ENDPOINT=dev-post-security-aurora-rds-databasecluster-12i5eo3fmmqel.cluster-cotm4atjnnku.us-east-1.rds.amazonaws.com

    # API Endpoint
    RBAC_API_ENDPOINT=dev-private-cpu-ecs.api.loyalty.com
    SECURITY_API_ENDPOINT=dev-private-cpu-ecs.api.loyalty.com
  elif [[ $CHOSEN_ENV == "uat" ]]; then
    BASTION_HOST=***********
    PEM_KEY=uat-us-east-1-root-kp.pem
    # RDS Endpoints
    RBAC_RDS_CLUSTER_ENDPOINT=uat-offers-rbac-aurora-rds-databasecluster-gj3bi7bhi2f1.cluster-cppfowopm0r2.us-east-1.rds.amazonaws.com
    SECURITY_RDS_CLUSTER_ENDPOINT=uat-post-security-aurora-rds-databasecluster-vfx4132nal4c.cluster-cppfowopm0r2.us-east-1.rds.amazonaws.com

    # API Endpoint
    RBAC_API_ENDPOINT=
    SECURITY_API_ENDPOINT=
  #elif [[ $CHOSEN_ENV = "prod" ]]
  #then
  #BASTION_HOST=***********
  else
    echo "Invalid Environment $CHOSEN_ENV. Acceptable environments are dev, uat, prod."
    exit
  fi

  check_for_pem $1

}

setup_ssh_tunnels_for_migration() {
  ENDPOINT=
  if [[ $1 == "PSM" ]]; then
    ENDPOINT=$SECURITY_RDS_CLUSTER_ENDPOINT
  elif [[ $1 == "RBAC" ]]; then
    ENDPOINT=$RBAC_RDS_CLUSTER_ENDPOINT
  fi

  ssh -i $FULL_PEM_PATH -f "$BASTION_USER@$BASTION_HOST" -L "3306:$ENDPOINT:$AWS_DB_PORT" -N
  if [[ $? -eq 0 ]]; then
    echo "Successfully created SSH tunnel to RBAC Summary RDS"
  else
    echo "Failed to create SSH Tunnel to RBAC Summary RDS"
    exit
  fi

}

setup_ssh_tunnels() {
  # ssh -i dev-us-east-1-root-kp.pem -f ec2-user@*********** -L 3308:dev-transaction-summary-aurora-rd-databasecluster-18a4cm4j1ndz4.cluster-ro-cotm4atjnnku.us-east-1.rds.amazonaws.com:3306 -N
  ssh -i $FULL_PEM_PATH -f "$BASTION_USER@$BASTION_HOST" -L "$LOCAL_RBAC_DB_PORT:$RBAC_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT" -N
  if [[ $? -eq 0 ]]; then
    echo "Successfully created SSH tunnel to RBAC Summary RDS"
  else
    echo "Failed to create SSH Tunnel to RBAC Summary RDS"
    exit
  fi

  # ssh -i dev-us-east-1-root-kp.pem -f ec2-user@*********** -L 3308:dev-transaction-summary-aurora-rd-databasecluster-18a4cm4j1ndz4.cluster-ro-cotm4atjnnku.us-east-1.rds.amazonaws.com:3306 -N
  ssh -i $FULL_PEM_PATH -f "$BASTION_USER@$BASTION_HOST" -L "$LOCAL_SECURITY_DB_PORT:$SECURITY_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT" -N
  if [[ $? -eq 0 ]]; then
    echo "Successfully created SSH tunnel to Security RDS"
  else
    echo "Failed to create SSH Tunnel to Security RDS"
    exit
  fi

  # # ssh -i dev-us-east-1-root-kp.pem -f ec2-user@*********** -L 8081:dev-private-cpu-ecs.api.loyalty.com:443 -N
  ssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_RBAC_API_PORT:$RBAC_API_ENDPOINT:$AWS_API_PORT -N
  if [[ $? -eq 0 ]]; then
    echo "Successfully created SSH tunnel to RBAC API"
  else
    echo "Failed to create SSH Tunnel to RBAC API"
    exit
  fi

  #   # ssh -i dev-us-east-1-root-kp.pem -f ec2-user@*********** -L 8081:dev-private-cpu-ecs.api.loyalty.com:443 -N
  ssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_SECURITY_API_PORT:$SECURITY_API_ENDPOINT:$AWS_API_PORT -N
  if [[ $? -eq 0 ]]; then
    echo "Successfully created SSH tunnel to Security API"
  else
    echo "Failed to create SSH Tunnel to Security API"
    exit
  fi
}

check_for_pem() {
  if [[ $# -eq 1 ]]; then
    PATH_TO_PEM=$2
  else

    read -r -p "Path to the PEM_KEY? (Default: Current directory)" USER_PATH

    if [[ $USER_PATH ]]; then
      PATH_TO_PEM=$USER_PATH
    fi
  fi

  FULL_PEM_PATH=$PATH_TO_PEM$PEM_KEY

  # Check if PEM exists
  if [[ -f $FULL_PEM_PATH ]]; then
    echo "Found $FULL_PEM_PATH."
  else
    echo "$FULL_PEM_PATH not found."
    exit 1
  fi
}

kill_pid() {
  echo "Killing Process with PID: $PID..."
  taskkill //f //pid $PID
}

print_commands_to_terminal() {
  echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_RBAC_DB_PORT:$RBAC_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT -N"

  echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_RBAC_API_PORT:$RBAC_API_ENDPOINT:$AWS_API_PORT -N"

  echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_SECURITY_DB_PORT:$SECURITY_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT -N"

  echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L $LOCAL_SECURITY_API_PORT:$SECURITY_API_ENDPOINT:$AWS_API_PORT -N"

  echo -e "\n"
}

print_rds_ssh_commands_to_terminal() {
  if [[ $1 == "PSM" ]]; then
    echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L 3306:$SECURITY_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT -N"
  elif [[ $1 == "RBAC" ]]; then
    echo -e "\nCommand:\nssh -i $FULL_PEM_PATH -f $BASTION_USER@$BASTION_HOST -L 3306:$RBAC_RDS_CLUSTER_ENDPOINT:$AWS_DB_PORT -N"
  fi
  echo -e "\n"
}

if [[ $# -eq 0 ]]; then
  echo "Choose your option:
	1: Set up SSH Tunnels
	2: Kill a SSH Process 
	3: Set up SSH Tunnel for RDS Migration
	"
  read -p "Option (1 or 2 or 3): " COMMAND
  if [[ $COMMAND -ne 1 ]] && [[ $COMMAND -ne 2 ]] && [[ $COMMAND -ne 3 ]]; then
    echo "Invalid Command"
    exit
  fi
else
  COMMAND=$1
fi

# Set up SSH Tunnels
if [[ $COMMAND -eq 1 ]]; then

  setup_ssh_urls $2

  print_commands_to_terminal

  setup_ssh_tunnels

  echo "Successfully set up SSH Tunnels to RBAC and Post Security Manager RDS, RBAC API and Poset Security Manager API on $CHOSEN_ENV"
# Kill tunnels
elif [[ $COMMAND -eq 2 ]]; then
  if [[ $# -eq 2 ]]; then
    PID = $2
    kill_pid
  else
    ps | grep ssh
    read -p "Enter a PID to kill (q to quit): " PID
    while [[ $PID -ne "q" ]]; do
      kill_pid
      read -p "Enter a PID to kill (q to quit): " PID
    done
  fi
# set up SSH Tunnel for RDS Migration
elif [[ $COMMAND -eq 3 ]]; then

  setup_ssh_urls $2
  read -p "Are you migrating PSM or RBAC?: " CHOSEN_DB
  if [[ -z $CHOSEN_DB ]]; then
    echo "No DB Chosen. Exiting..."
    exit
  fi
  if [[ $CHOSEN_DB -ne "PSM" ]] && [[ $CHOSEN_DB -ne "RBAC" ]]; then
    echo "Invalid Database. Exiting..."
    exit
  fi

  print_rds_ssh_commands_to_terminal $CHOSEN_DB

  setup_ssh_tunnels_for_migration $CHOSEN_DB

fi
