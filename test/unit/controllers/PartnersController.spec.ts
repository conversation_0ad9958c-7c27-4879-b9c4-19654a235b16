import { expect, assert } from 'chai';  
import * as sinon from 'sinon';  
import { PartnersController } from "../../../src/controllers/PartnersController";
import * as errors from "restify-errors";
// tslint:disable-next-line:no-var-requires

describe("PartnersController", () => {
  let partnersController: PartnersController;
  let partnersService: any;
  let req: any;
  let res: any;
  let next: any;

  before(() => {
    partnersService = sinon.fake();
    req = sinon.fake();
    res = sinon.fake();
    res.send = sinon.fake();
    next = sinon.fake();
    partnersController = new PartnersController(partnersService);
  });

  describe("On Get Partners", () => {
    it("should return partners", (done) => {
      partnersService.getAllPartners = sinon.fake.returns({results: [
        {
          "name": "Rexall",
          "sponsorCodes": [
            "RXAL"
          ],
          "type": [
            "in-store",
            "cash",
            "load+go"
          ],
        }
      ]});
      assert.doesNotThrow(async () => {
        await partnersController.onGetPartners(req, res, next);
        done();
      });
    });

    it("should throw an Error if the Partners API is down", (done) => {
      partnersService.getAllPartners = sinon.fake.throws(new errors.InternalError());
      partnersController.onGetPartners(req, res, next)
      .catch((err) => {
        assert.isTrue(err instanceof errors.HttpError);
        done();
      });
    });
  });

});
