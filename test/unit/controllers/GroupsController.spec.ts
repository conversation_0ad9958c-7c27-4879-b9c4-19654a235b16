import { GroupsController } from "../../../src/controllers/GroupsController";

import { Group } from "../../../src/entity/Group";
import { Partner } from "../../../src/model/Partner";
import { expect, assert } from "chai";
import * as sinon from "sinon";
import errors = require("restify-errors");
import { API_ERROR_CODES, apiErrorWrapper } from "../../../src/constants";
import { UserGroup } from "../../../src/entity/UserGroup";

import * as _ from "lodash";
import { User } from "../../../src/entity/User";

describe("GroupsController", () => {
  let groupsController: GroupsController;
  let groupsService: any;
  let partnersService: any;
  let usersService: any;
  let req: any;
  let res: any;
  let next: any;

  before(() => {
    groupsService = sinon.fake();
    partnersService = sinon.fake();
    usersService = sinon.fake();
    groupsController = new GroupsController(
      groupsService,
      partnersService,
      usersService
    );
  });

  beforeEach(() => {
    req = sinon.fake();
    res = sinon.fake();
    req.header = sinon.fake.returns("test-email");

    res.send = sinon.fake();
    next = sinon.fake();
  });

  describe("On get Groups", () => {
    const group: Group = {
      id: 1,
      name: "Group1",
      partnerMappings: [
        {
          partnerId: "1230",
          groupId: 1
        }
      ],
      coversAllPartners: false,
      isEditable: true
    };
    const groups = [group];
    const partners = [
      {
        id: "1230"
      }
    ];

    it("should return groups with partner details", done => {
      groupsService.getAllGroups = sinon.fake.returns(groups);
      const fakeGroups = _.cloneDeep(groups);
      fakeGroups[0].partners = partners;
      groupsService.getGroupsWithPartnerDetails = sinon.fake.returns(
        fakeGroups
      );
      res.send = (responseGroups: Group[]) => {
        expect(groupsService.getAllGroups.calledOnce).equals(true);
        expect(groupsService.getGroupsWithPartnerDetails.calledOnce).equals(
          true
        );
        expect(responseGroups.length).to.equal(1);
        expect(responseGroups[0].partners[0].id).to.equal(partners[0].id);
        done();
      };

      groupsController.onGetGroups(req, res, next);
    });
  });

  describe("Get Group by id", () => {
    it("should return the correct group if it exists", done => {
      req.params = sinon.fake.returns({ id: 1 });

      groupsService.doesGroupExist = sinon.fake.returns([
        {
          id: 1,
          partnerMappings: [
            {
              partnerId: 1
            }
          ]
        }
      ]);
      partnersService.getPartnersByIds = sinon.fake.returns({
        results: [
          {
            id: 1
          }
        ]
      });
      assert.doesNotThrow(() => {
        groupsController.getGroupById(req, res, next).then(() => done());
      });
    });

    it("should throw an error if the group does not exist", done => {
      req.params = sinon.fake.returns({ id: 1 });
      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.NotFoundError()
      );
      groupsController.getGroupById(req, res, next).catch(err => {
        assert.isNotNull(err);
        done();
      });
    });

    it("should throw an error if no group ID is passed", done => {
      req.params = {};
      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.BadRequestError()
      );
      groupsController.getGroupById(req, res, next).catch(err => {
        assert.isNotNull(err);
        done();
      });
    });
  });

  describe("Post Group", () => {
    it("should return group", done => {
      const newGroup = new Group();
      newGroup.name = "NAME";
      groupsService.addNewGroup = sinon.fake.returns(newGroup);
      usersService.doesUserExist = sinon.fake.returns(new User());
      res.send = (role: Group) => {
        expect(role.id).to.equal(newGroup.id);
        done();
      };
      req.body = {
        name: newGroup.name
      };
      groupsController.onAddGroup(req, res, next);
    });
  });

  describe("Add partner to group", () => {
    it("Should add a partner to a group", done => {
      const groupId: number = 124;
      const partnerId: string = "1";
      const group: Partial<Group> = {
        id: groupId,
        partnerMappings: [{ partnerId, groupId }]
      };

      req.params = { groupId };
      req.body = { partnerId };
      usersService.doesUserExist = sinon.fake.returns(new User());

      res.send = (sentGroup: Group) => {
        expect(sentGroup.id).to.equal(groupId);
        expect(sentGroup.partnerMappings.length).to.equal(
          group.partnerMappings.length
        );
        expect(groupsService.addNewGroup.calledOnce).equals(true);
        expect(groupsService.addPartnerToGroup.calledOnce).equals(true);
        expect(groupsService.getGroupById.calledOnce).equals(true);
        expect(partnersService.getPartnersByIds.calledOnce).equals(true);
        done();
      };

      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.resolves(null);
      groupsService.addPartnerToGroup = sinon.fake.returns(group);
      groupsService.getGroupById = sinon.fake.returns(group);
      partnersService.getPartnersByIds = sinon.fake.returns([new Partner()]);
      groupsController.addPartnerToGroup(req, res, next);
    });
    it("Should throw an error if no groupID is passed", () => {
      req.params = { groupId: null };
      req.body = { partnerId: null };
      res.send = sinon.fake();
      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );
      groupsController.addPartnerToGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
    it("should throw an error if no partnerID is passed", () => {
      const groupId: number = 1234;
      req.params = { groupId };
      req.body = {};
      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
        )
      );
      groupsController.addPartnerToGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.message
        );
      });
    });

    it("should throw an error if no group with the groupID exists", () => {
      const groupId: number = 1234;
      const partnerId: string = "1";

      req.params = { groupId };
      req.body = { partnerId };

      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.NotFoundError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );
      groupsController.addPartnerToGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
    it("should throw an error if no partner with that partnerID exists", () => {
      const groupId: number = 1234;
      const partnerId: string = "1";

      req.params = { groupId };
      req.body = { partnerId };

      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.throws(
        new errors.NotFoundError(
          apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
        )
      );

      groupsService.getGroupById = sinon.fake.returns(new Group());
      partnersService.getPartnersByIds = sinon.fake.returns({ results: [] });

      groupsController.addPartnerToGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.message
        );
      });
    });
  });

  describe("delete partner from group", () => {
    it("should delete a partner from a group with a partnerId", done => {
      const groupId: number = 124;
      const partnerId: string = "1";
      const group: Partial<Group> = {
        id: groupId,
        partnerMappings: [{ partnerId, groupId }]
      };

      req.params = { groupId, partnerId };
      usersService.doesUserExist = sinon.fake.returns(new User());

      res.send = (sentGroup: Group) => {
        expect(sentGroup.id).to.equal(groupId);
        expect(groupsService.addNewGroup.calledOnce).equals(true);
        expect(groupsService.deletePartnerFromGroup.calledOnce).equals(true);
        expect(groupsService.getGroupById.calledOnce).equals(true);
        expect(partnersService.getPartnersByIds.calledOnce).equals(true);
        done();
      };

      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.resolves(null);
      groupsService.deletePartnerFromGroup = sinon.fake();
      groupsService.getGroupById = sinon.fake.returns(group);
      partnersService.getPartnersByIds = sinon.fake.returns({
        results: [new Partner()]
      });

      groupsController.deletePartnerFromGroup(req, res, next);
    });
    it("Should throw an error if no groupID is passed", () => {
      req.params = { groupId: null };
      req.body = { partnerId: null };
      res.send = sinon.fake();
      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );
      groupsController.deletePartnerFromGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
    it("should throw an error if no partnerID is passed", () => {
      const groupId: number = 1234;
      req.params = { groupId };
      req.body = {};
      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
        )
      );
      groupsController.deletePartnerFromGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.message
        );
      });
    });

    it("should throw an error if no group with the groupID exists", () => {
      const groupId: number = 1234;
      const partnerId: string = "1";

      req.params = { groupId, partnerId };

      groupsService.doesGroupExist = sinon.fake.throws(
        new errors.NotFoundError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );

      groupsController.deletePartnerFromGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
    it("should throw an error if no partner with that partnerID exists", () => {
      const groupId: number = 1234;
      const partnerId: string = "1";

      req.params = { groupId, partnerId };

      groupsService.doesGroupExist = sinon.fake.resolves(null);
      partnersService.doesPartnerExist = sinon.fake.throws(
        new errors.NotFoundError(
          apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
        )
      );

      groupsController.deletePartnerFromGroup(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.PARTNER_NOT_FOUND.message
        );
      });
    });
  });

  describe("Get users by Group By ID", () => {
    const groupId: number = 1;
    it("Should return a list of users", done => {
      req.params = { groupId };
      groupsService.getUsersByGroupId = sinon.fake.returns([
        new UserGroup(),
        new UserGroup()
      ]);
      groupsService.doesGroupExist = sinon.fake.returns(null);
      res.send = (responseUsers: UserGroup[]) => {
        expect(responseUsers.length).equals(2);
        done();
      };

      groupsController.getUsersByGroupId(req, res, next);
    });
    it("Should throw an error if the group does not exist", () => {
      req.params = { groupId };
      groupsService.doesGroupExist = sinon.fake.returns(null);
      groupsService.getUsersByGroupId = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );
      groupsController.getUsersByGroupId(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
    it("Should throw an error if no groupId is passed", () => {
      req.params = { groupId: "" };
      groupsService.doesGroupExist = sinon.fake.returns(null);
      groupsService.getUsersByGroupId = sinon.fake.throws(
        new errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
        )
      );
      groupsController.getUsersByGroupId(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        expect(error.context.message).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.message
        );
      });
    });
  });
});
