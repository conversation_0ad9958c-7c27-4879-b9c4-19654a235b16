import { expect } from "chai";
import * as sinon from "sinon";
import { UsersController } from "../../../src/controllers/UsersController";
import { UserGroup } from "../../../src/entity/UserGroup";
import { UserPartnerDirect } from "../../../src/entity/UserPartnerDirect";
import { Group } from "../../../src/entity/Group";
import * as _ from "lodash";
import { User } from "../../../src/entity/User";
import { Partner } from "../../../src/model/Partner";

describe("UsersController", () => {
  let usersController: UsersController;
  let groupsService: any;
  let usersService: any;
  let partnersService: any;

  let req: any;
  let res: any;
  let next: any;

  before(() => {
    groupsService = sinon.fake();
    partnersService = sinon.fake();
    usersService = sinon.fake();
    usersController = new UsersController(
      groupsService,
      usersService,
      partnersService
    );
  });

  beforeEach(() => {
    req = sinon.fake();
    req.header = sinon.fake.returns("test-email");
    res = sinon.fake();
    res.send = sinon.fake();
    next = sinon.fake();
  });

  describe("Groups", () => {
    let userId;
    let groupId;
    let group;
    let groups;
    let partners;
    let partnerId;
    let user;
    beforeEach(() => {
      userId = "<EMAIL>";
      groupId = 1234;
      partnerId = "partnerId";
      group = {
        id: groupId,
        name: "Group1",
        partnerMappings: [{ partnerId, groupId }]
      };
      groups = [group];
      partners = [
        {
          id: partnerId
        }
      ];
      user = new User();
      user.id = userId;

      usersService.doesUserExist = sinon.fake.returns(new User());
      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      const fakeGroups = _.cloneDeep(groups);
      fakeGroups[0].partners = partners;
      groupsService.getGroupsWithPartnerDetails = sinon.fake.returns(
        fakeGroups
      );
    });

    describe("Get groups for user", () => {
      it("Should return groups for a user", done => {
        req.params = { userId };

        res.send = (responseGroups: Group[]) => {
          expect(responseGroups.length).to.equal(1);
          expect(responseGroups[0].partners[0].id).to.equal(partners[0].id);
          done();
        };

        usersController.onGetGroupsForUser(req, res, next);
      });
    });

    describe("Add group to user", () => {
      const userGroup = new UserGroup();
      userGroup.userId = userId;
      userGroup.groupId = groupId;

      it("Should add a group to a user", done => {
        req.params = { userId };
        req.body = { groupId };

        res.send = (responseGroups: Group[]) => {
          expect(responseGroups.length).equals(groups.length);
          expect(groupsService.doesGroupExist.calledOnce).equals(true);
          expect(usersService.addUser.calledOnce).equals(false);
          expect(usersService.addGroupToUser.calledOnce).equals(true);
          done();
        };

        usersService.getUserById = sinon.fake.returns(user);
        usersService.addUser = sinon.fake.returns(user);
        groupsService.doesGroupExist = sinon.fake.returns(null);
        usersService.addGroupToUser = sinon.fake.returns(userGroup);

        usersController.addGroupToUser(req, res, next);
      });

      it("Should add a user if user does not exist", done => {
        req.params = { userId };
        req.body = { groupId };

        res.send = (responseGroups: Group[]) => {
          expect(responseGroups.length).equals(groups.length);
          expect(usersService.addUser.calledOnce).equals(true);
          expect(usersService.addGroupToUser.calledOnce).equals(true);
          done();
        };

        usersService.getUserById = sinon.fake.returns(null);
        usersService.addUser = sinon.fake.returns(user);
        groupsService.doesGroupExist = sinon.fake.returns(null);
        usersService.addGroupToUser = sinon.fake.returns(userGroup);

        usersController.addGroupToUser(req, res, next);
      });
    });

    describe("Remove group from user", () => {
      const userGroup = new UserGroup();
      userGroup.userId = userId;
      userGroup.groupId = groupId;

      it("Should remove a group from a user", done => {
        req.params = { userId, groupId };

        res.send = (responseGroups: Group[]) => {
          expect(responseGroups.length).equals(groups.length);
          expect(groupsService.doesGroupExist.calledOnce).equals(true);
          expect(usersService.doesUserExist.calledOnce).equals(true);
          expect(usersService.removeGroupFromUser.calledOnce).equals(true);
          done();
        };

        usersService.doesUserExist = sinon.fake.returns(null);
        groupsService.doesGroupExist = sinon.fake.returns(null);
        usersService.removeGroupFromUser = sinon.fake.returns(null);

        usersController.removeGroupFromUser(req, res, next);
      });
    });
  });

  describe("Partners", () => {
    let userId;
    let groupId;
    let user;
    let partners;
    let partnerId;
    let groups;
    let partnerInGroupId;
    let allPartners;
    let otherPartnerId;

    beforeEach(() => {
      userId = "<EMAIL>";
      groupId = 1234;
      partnerId = "partnerId";
      otherPartnerId = "otherPartnerId";
      partnerInGroupId = "partnerInGroupId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      partners = [
        {
          id: partnerId
        }
      ];
      allPartners = [
        {
          id: partnerId
        },
        {
          id: otherPartnerId
        }
      ];
      groups = [
        {
          partnerMappings: [{ partnerId }, { partnerId: partnerInGroupId }]
        }
      ];

      usersService.doesUserExist = sinon.fake.returns(new User());
      usersService.getUserById = sinon.fake.returns(user);
      usersService.getPartnersForUser = sinon.fake.returns(partners);
    });

    describe("Get partners for user", () => {
      it("Should return partners for a user", done => {
        req.params = { userId };
        res.send = (responsePartners: Partner[]) => {
          expect(responsePartners.length).equals(partners.length);
          expect(usersService.doesUserExist.calledOnce).equals(true);
          expect(usersService.getPartnersForUser.calledOnce).equals(true);
          done();
        };
        usersController.onGetPartnersForUser(req, res, next);
      });
    });

    describe("Add partner to user", () => {
      const userPartnerDirect = new UserPartnerDirect();
      userPartnerDirect.userId = userId;
      userPartnerDirect.partnerId = partnerId;

      it("Should add a partner to a user", done => {
        req.params = { userId };
        req.body = { partnerId };

        res.send = (responsePartners: Partner[]) => {
          expect(responsePartners.length).equals(partners.length);
          expect(partnersService.doesPartnerExist.calledOnce).equals(true);
          expect(usersService.addPartnerToUser.calledOnce).equals(true);
          done();
        };

        partnersService.doesPartnerExist = sinon.fake.returns(null);
        usersService.addPartnerToUser = sinon.fake.returns(userPartnerDirect);

        usersController.addPartnerToUser(req, res, next);
      });
    });

    describe("Remove partner from user", () => {
      const userPartnerDirect = new UserPartnerDirect();
      userPartnerDirect.userId = userId;
      userPartnerDirect.partnerId = partnerId;

      it("Should remove a partner from a user", done => {
        req.params = { userId, partnerId };

        res.send = (responsePartners: Partner[]) => {
          expect(responsePartners.length).equals(partners.length);
          expect(usersService.doesUserExist.calledOnce).equals(true);
          expect(partnersService.doesPartnerExist.calledOnce).equals(true);
          done();
        };

        usersService.doesUserExist = sinon.fake.returns(null);
        partnersService.doesPartnerExist = sinon.fake.returns(null);
        usersService.removePartnerFromUser = sinon.fake.returns(null);

        usersController.removePartnerFromUser(req, res, next);
      });
    });
  });
});
