import { expect, assert } from "chai";
import { GroupsService } from "../../../src/services/groups.service";
import { Group } from "../../../src/entity/Group";
import sinon = require("sinon");
import errors = require("restify-errors");
import { ORM_ERRORS } from "../../../src/errors/orm.errors";
import Container from "typedi";
import { Partner } from "../../../src/model/Partner";
import { GroupPartnerMapping } from "../../../src/entity/GroupPartnerMapping";
import { QueryFailedError } from "typeorm";
import { API_ERROR_CODES } from "../../../src/constants";
import { UserGroup } from "../../../src/entity/UserGroup";

describe("GroupsService", () => {
  let groupsService: GroupsService;
  let groupRepository;
  let groupsPartnerRepository;
  let partnersService;
  const requestingUserId: string = "";

  before(() => {
    groupRepository = sinon.fake();
    groupsPartnerRepository = sinon.fake();
    partnersService = sinon.fake();
    Container.set("GroupRepository", groupRepository);
    Container.set("GroupPartnerMappingRepository", groupsPartnerRepository);
    groupsService = new GroupsService(partnersService);
  });

  describe("Get all groups", () => {
    it("should return groups", async () => {
      groupRepository.find = sinon.fake.returns([new Group()]);
      const groups = await groupsService.getAllGroups();
      expect(groupRepository.calledOnce);
      expect(groups.length).to.equal(1);
    });
  });

  describe("Add Group", () => {
    before(() => {
      groupRepository.create = sinon.fake.returns(new Group());
    });

    it("when add group returns group", async () => {
      const newGroup = new Group();
      newGroup.name = "newGroup";
      groupRepository.save = sinon.fake.returns(newGroup);
      const createdGroup = await groupsService.addNewGroup(
        newGroup,
        requestingUserId
      );

      expect(groupRepository.create.calledOnce);
      expect(groupRepository.save.calledOnce);
      expect(createdGroup.name).to.equal(newGroup.name);
    });

    it("when add duplicate role throws error", done => {
      const newGroup = new Group();
      newGroup.name = "newGroup";
      groupRepository.save = sinon.stub().throws({
        code: ORM_ERRORS.DUPLICATE_ENTRY
      });

      groupsService.addNewGroup(newGroup, requestingUserId).catch(error => {
        expect(error).instanceOf(errors.ConflictError);
        done();
      });
    });

    // it("when add role with long name throws error", (done) => {
    //   const newGroup = new Group();
    //   newGroup.name = "newGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroupnewGroup";

    //   groupsReadService.addNewGroup(newGroup).catch((error) => {
    //     expect(error).instanceOf(errors.InvalidContentError);
    //     done();
    //   });
    // });
  });

  describe("Get groups by Id", () => {
    it("Should return a group with a found id", async () => {
      const groupId = 1;
      groupRepository.findOne = sinon.fake.returns({
        id: groupId
      });
      const group = await groupsService.getGroupById(groupId);
      expect(groupRepository.findOne.calledOnce).equals(true);
      expect(group.id).equals(groupId);
    });

    it("Should return undefined if no groups are found", async () => {
      const groupId = 1;
      groupRepository.findOne = sinon.fake.returns(undefined);
      const group = await groupsService.getGroupById(groupId);
      expect(groupRepository.findOne.calledOnce).equals(true);
      assert.isUndefined(group);
    });
  });

  describe("Add Partner to Group by Id", () => {
    it("should add a partner to group if not already there", async () => {
      const groupId = 1;
      const partnerId = "1";
      groupsPartnerRepository.create = sinon.fake.returns(
        new GroupPartnerMapping()
      );
      groupsPartnerRepository.save = sinon.fake.returns(
        Object.assign(new Partner(), { groupId, partnerId })
      );

      const group = await groupsService.addPartnerToGroup(
        groupId,
        partnerId,
        requestingUserId
      );
      expect(group.groupId).equals(groupId);
      expect(groupsPartnerRepository.create.calledOnce).equals(true);
      expect(groupsPartnerRepository.save.calledOnce).equals(true);
    });

    it("should throw a conflict error if the partner already exists", async () => {
      const groupId = 1;
      const partnerId = "1";
      const duplicateError: QueryFailedError = new QueryFailedError(
        "test",
        [],
        "test"
      );
      Object.assign(duplicateError, { code: ORM_ERRORS.DUPLICATE_ENTRY });

      groupsPartnerRepository.create = sinon.fake.returns(
        new GroupPartnerMapping()
      );
      groupsPartnerRepository.save = sinon.fake.throws(duplicateError);

      groupsService
        .addPartnerToGroup(groupId, partnerId, requestingUserId)
        .catch(error => {
          expect(error.jse_info.code).to.equal(
            API_ERROR_CODES.PARTNER_DUPLICATE.code
          );
          expect(error.jse_info.message).to.equal(
            API_ERROR_CODES.PARTNER_DUPLICATE.message
          );
        });
      expect(groupsPartnerRepository.create.calledOnce).equals(true);
      expect(groupsPartnerRepository.save.calledOnce).equals(true);
    });

    it("should throw an internal error for other database failures", async () => {
      const groupId = 1;
      const partnerId = "1";
      const duplicateError: QueryFailedError = new QueryFailedError(
        "test",
        [],
        "test"
      );

      groupsPartnerRepository.create = sinon.fake.returns(
        new GroupPartnerMapping()
      );
      groupsPartnerRepository.save = sinon.fake.throws(duplicateError);

      groupsService
        .addPartnerToGroup(groupId, partnerId, requestingUserId)
        .catch(error => {
          assert.equal(error.body.code, "Internal");
        });
      expect(groupsPartnerRepository.create.calledOnce).equals(true);
      expect(groupsPartnerRepository.save.calledOnce).equals(true);
    });
  });

  describe("Delete partner from group by id", () => {
    it("should add a partner to group if not already there", async () => {
      const groupId = 1;
      const partnerId = "1";
      groupsPartnerRepository.findOne = sinon.fake.returns(
        Object.assign(new Partner(), { groupId, partnerId })
      );
      groupsPartnerRepository.delete = sinon.fake.returns({
        affected: 1
      });
      await groupsService.deletePartnerFromGroup(groupId, partnerId);
      expect(groupsPartnerRepository.findOne.calledOnce).equals(true);
      expect(groupsPartnerRepository.delete.calledOnce).equals(true);
    });

    it("should resolve if no partner is found to delete", async () => {
      const groupId = 1;
      const partnerId = "1";

      groupsPartnerRepository.findOne = sinon.fake.returns(undefined);
      groupsPartnerRepository.delete = sinon.fake();

      await groupsService.deletePartnerFromGroup(groupId, partnerId);
      expect(groupsPartnerRepository.findOne.calledOnce).equals(true);
      expect(groupsPartnerRepository.delete.called).equals(false);
    });
  });

  describe("get users by group Id", () => {
    const groupId: number = 1;
    it("Should return a list of users for the group", async () => {
      groupRepository.findOne = sinon.fake.returns({
        users: [new UserGroup()]
      });
      const numUsers: number = (await groupsService.getUsersByGroupId(1))
        .length;
      expect(numUsers).equals(1);
      expect(groupRepository.findOne.calledOnce).equals(true);
    });
  });

  describe("Does group exist", () => {
    const groupId = 100001;

    it("Should throw an error if no groupId is passed", done => {
      groupsService.doesGroupExist(null).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if invalid group is passed", done => {
      groupRepository.findOne = sinon.fake.returns(null);

      groupsService.doesGroupExist(groupId).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.GROUP_NOT_FOUND.code
        );
        done();
      });
    });
  });

  describe("Get Groups with Partners", () => {
    const groups: Group[] = [
      {
        name: "Group1",
        id: 1,
        partnerMappings: [
          {
            partnerId: "P1",
            groupId: 1
          }
        ],
        isEditable: true,
        coversAllPartners: false
      },
      {
        name: "Group2",
        id: 2,
        partnerMappings: [],
        isEditable: true,
        coversAllPartners: false
      }
    ];

    const partners: Partner[] = [
      {
        id: "P1"
      } as Partner,
      {
        id: "P2"
      } as Partner
    ];

    it("Get groups with partners", async () => {
      partnersService.getPartnersByIds = sinon.fake.returns(partners);
      const responseGroups = await groupsService.getGroupsWithPartnerDetails(
        groups
      );
      expect(partnersService.getPartnersByIds.calledOnce).equals(true);
      expect(responseGroups.length).to.equal(groups.length);
      expect(responseGroups[0].partners[0].id).to.equal(partners[0].id);
    });
  });
});
