import { expect } from "chai";
import { PartnersService } from "../../../src/services/partners.service";
import { API_ERROR_CODES } from "../../../src/constants";
// tslint:disable-next-line:no-var-requires
const sinon = require('sinon');

describe("PartnersService", () => {
  let partnersService: PartnersService;
  let config: any;

  before(() => {
    config = sinon.fake();
    config.config = sinon.fake();
    config.config.get = sinon.fake.returns("local");
    partnersService = new PartnersService(config);
  });

  describe("Get all partners", () => {
    it("should return all partners", async () => {
      const partners = await partnersService.getAllPartners();
      expect(partners.length).greaterThan(1);
    });
    
  });

  describe("Get partners by id", () => {
    it("should return some partners by id", async () => {
      const partnerIds = ['4a755252-876b-478e-9440-42961525e307'];
      const partners = await partnersService.getPartnersByIds(partnerIds);
      expect(partners.length).equals(1);
    });
    
  });

  describe("Does partner exist", () => {
    const partnerId = "100001";

    it ("Should throw an error if no partnerId is passed", (done) => {
      partnersService.doesPartnerExist(null)
      .catch((error) => {
        expect(error.context.code).to.equal(API_ERROR_CODES.PARTNER_NOT_FOUND.code);
        done();
      });
    });

    it ("Should throw an error if invalid partnerId is passed", (done) => {

      partnersService.doesPartnerExist(partnerId)
      .catch((error) => {
        expect(error.context.code).to.equal(API_ERROR_CODES.PARTNER_NOT_FOUND.code);
        done();
      });
      
    });
  });

});
