import { expect } from "chai";
import { OffersService } from "../../../src/services/offers.service";
import sinon = require("sinon");
import Container from "typedi";
import { Config } from "../../../src/config/config";

describe("OffersService", () => {
  let offersService: OffersService;
  let config: any;
  let req: any;

  before(() => {
    config = sinon.fake();
    config.config = sinon.fake();
    config.config.get = key => {
      switch (key) {
        case "useMockOffers":
          return true;
        case "useMockBulkJobs":
          return true;
        case "offersEndpoint":
          return "http://localhost:9000";
        case "useMockContent":
          return true;
        case "contentEndpoint":
          return "http://localhost:9000";
      }
    };
    Container.set("Config", config);
    offersService = new OffersService(config);
  });

  describe("Bulk Upload Offers", () => {
    it("should get bulk Id", async () => {
      req = {
        method: "POST",
        body: {
          bulkName: "NewBulkJob"
        }
      };
      const bulkResult = await offersService.uploadBulkOffers(req);
      expect(bulkResult.id).equals("bulkId");
    });

    // TODO: fix these when we stop hardcoding the mock for POST bulk uploads
    // it("should throw 500 error", (done) => {
    //   req = {
    //     method: "POST",
    //     body: {
    //       bulkName: "500Error"
    //     }
    //   }
    //   offersService.uploadBulkOffers(req)
    //   .catch(error => {
    //     expect(error.context.code).equals("500_ERROR");
    //     done();
    //   });

    // });

    // it("should throw 400 error", (done) => {
    //   req = {
    //     method: "POST",
    //     body: {
    //       bulkName: "400Error"
    //     }
    //   }
    //   offersService.uploadBulkOffers(req)
    //   .catch(error => {
    //     expect(error.context.code).equals("400_ERROR");
    //     done();
    //   });
    //
    // });
  });
});