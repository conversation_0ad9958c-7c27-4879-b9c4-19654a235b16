AWSTemplateFormatVersion: "2010-09-09"
Description: Creates a service / task def for post acl node ecs
Parameters:
  AppMemory:
    Type: Number
    Description: Soft memory of app container
    Default: 512
  AppCpu:
    Type: Number
    Description: cpu units of app container
    Default: 256
  ContainerTotalCpu:
    Type: Number
    Description: cpu units of all containers (secure-proxy uses 256 of this by default)
    Default: 512
  ContainerTotalMemory:
    Type: Number
    Description: Soft memory of all containers (secure-proxy uses 512mb of this by default)
    Default: 1024
  ClusterStackName:
    Description: Name of an active CloudFormation stack that contains an ECS cluster
    Type: String
    MinLength: 1
    MaxLength: 255
    AllowedPattern: ^[a-zA-Z][-a-zA-Z0-9]*$
  AppName:
    Type: String
    Description: Name of app. Should be the same as docker repository name.
    Default: post-security-manager
  AppVersion:
    Type: String
    Description: Version label of app
  AppContainerPort:
    Type: Number
    Description: Port the app runs on in the image
    Default: "8081"
  ImageRepository:
    Type: String
    Description:
      The URI of the image repository where the app is published. Do not
      include app name in URI.
    Default: 277983268692.dkr.ecr.us-east-1.amazonaws.com
  AppDesiredCount:
    Type: Number
    Description: Number of instances of the service to run
    Default: "1"
  AppMaxCount:
    Type: Number
    Description: Max number of instances of the service to scale out to
    Default: "3"
  AppMinCount:
    Type: Number
    Description: Min number of instances of the service to scale in to
    Default: "1"
  AutoScaleHighThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered high
    Default: "70"
  AutoScaleLowThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered low
    Default: "25"
  LogRetention:
    Type: Number
    Description: Number of days to retain logs in CWL
    Default: "14"
  NLBListenerPort:
    Type: Number
    Description: Port the NLB listens on
    Default: "1121"
  KmsKey:
    Type: String
    Description: The ARN of a KMS CMK to use to decrypt secure environment variables
  DeregistrationDelay:
    Type: Number
    Description: (second) The timeout of connection draing for the each task
    Default: "60"
  PagerDutyURL:
    Type: String
    Description: Pager Duty endpoint
  Environment:
    Type: String
    Description: Environment Name
  EnvironmentType:
    Type: String
    Description: Environment Type
  NetworkStackName:
    Description: Name of the Network Stack. i.e 'AMRPWL-Dev/AMRPWL-Int/AMRPWL-UAT/AMRPWL-Load'
    Type: String
  AgilityCredentialsSecretId:
    Description: Secret ID for agility credentials
    Type: String
  AgilityTokenSecretId:
    Description: Secret ID for agility token
    Type: String
  KinesisForSplunkStackName:
    Description: The name of Kinesis which is unique for each environment, used for logging to splunk
    Type: String
  MemoryReservationThreshold:
    Type: Number
    Description: Percentage of memory reserved considered high
    Default: '80'
  HealthyHostThreshold:
    Type: Number
    Description: healthy host alarm threshold
    Default: '1'
  CPUHighThresholdPagerDuty:
    Type: Number
    Description: Percentage of service memory utilization that is considered high and pagerduty notification sent
    Default: '90'
Conditions:
  IsDevEnvironment: !Equals [ !Ref 'Environment', 'dev' ]
Resources:
  NLBTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 443
      Protocol: TCP
      TargetType: ip
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: "20"
      VpcId: !ImportValue
        Fn::Sub: ${ClusterStackName}-VpcId
      Tags:
        - Key: Name
          Value: !Sub "ECS Target Group - ${AWS::StackName}"
  NLBListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref "NLBTargetGroup"
      LoadBalancerArn: !ImportValue
        Fn::Sub: ${ClusterStackName}-NetworkLoadBalancerARN
      Port: !Ref "NLBListenerPort"
      Protocol: TCP
  EcsTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: ""
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      Policies:
        - PolicyName: ECS-CMK-Decrypt-Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - kms:Decrypt
                Resource:
                  - !Ref "KmsKey"
        - PolicyName: "agility-secrets-manager-access"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Action:
                  - "secretsmanager:DescribeSecret"
                  - "secretsmanager:GetSecretValue"
                  - "secretsmanager:ListSecretVersionIds"
                Effect: "Allow"
                Resource:
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AgilityCredentialsSecretId}*"
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AgilityTokenSecretId}*"
  EcsExecutionTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      ContainerDefinitions:
        - Name: !Ref "AppName"
          Image: !Sub "${ImageRepository}/${AppName}:${AppVersion}"
          Cpu: !Ref "AppCpu"
          PortMappings:
            - ContainerPort: !Ref "AppContainerPort"
          Memory: !Ref "ContainerTotalMemory"
          Essential: "true"
          HealthCheck:
            StartPeriod: 20
            Command:
              - "CMD-SHELL"
              - !Sub "curl -f http://localhost:${AppContainerPort}/health || exit 123"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref "LogGroup"
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: ecs
        - Name: secure-proxy
          Image: !Sub '277983268692.dkr.ecr.${AWS::Region}.amazonaws.com/secure-proxy:${Environment}'
          Cpu: 256
          MemoryReservation: !Ref 'AppMemory'
          VersionConsistency: disabled
          PortMappings:
            - ContainerPort: 443
          Environment:
            - Name: BACKEND_TARGET
              Value: !Sub 'http://127.0.0.1:${AppContainerPort}'
          Ulimits:
            - Name: nofile
              SoftLimit: 32768
              HardLimit: 32768
          Essential: "true"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref "LogGroup"
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: ecs
      Volumes: []
      Family: !Ref "AWS::StackName"
      TaskRoleArn: !Ref "EcsTaskRole"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref 'EcsExecutionTaskRole'
      Cpu: !Ref 'ContainerTotalCpu'
      Memory: !Ref 'ContainerTotalMemory'
  Service:
    Type: AWS::ECS::Service
    DependsOn:
      - NLBListener
      - LogGroup
    Properties:
      LaunchType: FARGATE
      ServiceName: !Ref "AWS::StackName"
      TaskDefinition: !Ref "TaskDefinition"
      DesiredCount: !Ref "AppDesiredCount"
      LoadBalancers:
        - TargetGroupArn: !Ref "NLBTargetGroup"
          ContainerPort: 443
          ContainerName: secure-proxy
      Cluster: !ImportValue
        Fn::Sub: ${ClusterStackName}-ClusterName
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: DISABLED
          Subnets:
            - !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-A-SubID"
            - !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-B-SubID"
            - !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-C-SubID"
          SecurityGroups:
            - !Ref 'AppSecurityGroup'
  AppSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub '${Environment}-${AppName}-sg'
      GroupDescription: Allow HTTPS from NLB
      VpcId: !ImportValue
        Fn::Sub: ${ClusterStackName}-VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LB-AZ-A-CIDR"
          Description:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-A-SubID"
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LB-AZ-B-CIDR"
          Description:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-B-SubID"
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LB-AZ-C-CIDR"
          Description:
            !ImportValue
              'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-C-SubID"
      Tags:
      - Key: Name
        Value: !Sub '${Environment}-${AppName}-sg'
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      RetentionInDays: !Ref "LogRetention"
      LogGroupName: !Ref "AWS::StackName"
  SubscriptionFilter:
    DependsOn: LogGroup
    Type: 'AWS::Logs::SubscriptionFilter'
    Properties:
      RoleArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Role-Arn'
      LogGroupName:
        Ref: LogGroup
      FilterPattern: ''
      DestinationArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Stream-Arn'
  EcsAutoScaleRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole
  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref "AppMaxCount"
      MinCapacity: !Ref "AppMinCount"
      ResourceId: !Join
        - /
        - - service
          - !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
          - !GetAtt "Service.Name"
      RoleARN: !GetAtt "EcsAutoScaleRole.Arn"
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
  ScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Join
        - "-"
        - - !GetAtt "Service.Name"
          - ScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId: !Ref "ScalableTarget"
      StepScalingPolicyConfiguration:
        AdjustmentType: PercentChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            MetricIntervalUpperBound: 10
            ScalingAdjustment: 10
          - MetricIntervalLowerBound: 10
            ScalingAdjustment: 30
  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Join
        - "-"
        - - !GetAtt "Service.Name"
          - ScaleDownPolicy
      PolicyType: StepScaling
      ScalingTargetId: !Ref "ScalableTarget"
      StepScalingPolicyConfiguration:
        AdjustmentType: PercentChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: -10
            MetricIntervalUpperBound: 0
            ScalingAdjustment: -10
          - MetricIntervalUpperBound: -10
            ScalingAdjustment: -30
  CPUUtilAlarmHigh:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: cpu utilization alarm for ECS service for high cpu usage
      AlarmActions:
        - !Ref "ScaleUpPolicy"
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: "60"
      EvaluationPeriods: "2"
      Threshold: !Ref "AutoScaleHighThreshold"
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt "Service.Name"
  CPUUtilAlarmLow:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: cpu utilization alarm for ECS service for low cpu usage
      AlarmActions:
        - !Ref "ScaleDownPolicy"
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: "60"
      EvaluationPeriods: "2"
      Threshold: !Ref "AutoScaleLowThreshold"
      TreatMissingData: ignore
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt "Service.Name"
  HealthyHostAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: At least one healthy host not present
      AlarmActions:
        - !Ref 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: HealthyHostCount
      Namespace: AWS/NetworkELB
      Statistic: SampleCount
      Period: '60'
      EvaluationPeriods: '1'
      Threshold: !Ref 'HealthyHostThreshold'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      TreatMissingData: ignore
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: TargetGroup
          Value: !GetAtt 'NLBTargetGroup.TargetGroupFullName'
        - Name: LoadBalancer
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-LoadBalancerFullName        
  AlarmSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
        - Endpoint: !Sub '${PagerDutyURL}'
          Protocol: https
      TopicName: !Sub '${AWS::StackName}-SNS-Alarm'
  MemoryReservation:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: This alarm helps you detect a high memory reservation of the ECS cluster
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: MemoryReservation
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '5'
      Threshold: !Ref 'MemoryReservationThreshold'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  CPUUtilAlarmHighPagerDuty:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: cpu utilization alarm for ECS service for high cpu usage
      AlarmActions:
        - !Ref 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '2'
      Threshold: !Ref 'CPUHighThresholdPagerDuty'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
Outputs:
  Service:
    Description: The name of the ECS service created
    Value: !GetAtt "Service.Name"
    Export:
      Name: !Sub "${AWS::StackName}-ServiceName"
  TaskFamily:
    Description: The family of the task created for the service
    Value: !Ref "AWS::StackName"
  TaskArn:
    Description: The ARN of the task created for the service
    Value: !Ref "TaskDefinition"
  LogGroup:
    Description: The name of the log group created for the app
    Value: !Ref "LogGroup"
    Export:
      Name: !Sub "${AWS::StackName}-LogGroupName"
  NLBListenerPort:
    Description: The port the network load balancer is listening on for the app
    Value: !Ref "NLBListenerPort"
    Export:
      Name: !Sub "${AWS::StackName}-NLBListenerPort"