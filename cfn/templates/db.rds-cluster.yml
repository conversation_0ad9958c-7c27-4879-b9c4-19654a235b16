AWSTemplateFormatVersion: "2010-09-09"
Description: Create RDS Aurora(postgres/mysql) cluster with one reader and writer instance and auto scaling
# Copied from: https://github.com/LoyaltyOne/apollo-platform/tree/master/common/rds

# This can be run by running the command below. It needs the assumed profile found below:
# https://github.com/LoyaltyOne/apollo-platform/tree/master/scripts/saml-login

# NOTE: in order for this to work, the db.rds-cluster-parameter-group.yml needs to be deployed

# aws cloudformation create-stack --stack-name dev-offers-rbac-aurora-rds --template-body file://cfn/templates/db.rds-cluster.yml --parameters file://env/db.dev.params.json --profile assumed_role

Parameters:
  DBName:
    Description: The database name
    Type: String
    MinLength: 1
    MaxLength: 25
  DBUser:
    Description: The database admin account username
    Type: String
    MinLength: 1
    MaxLength: 16
    AllowedPattern: "[a-zA-Z][a-zA-Z0-9]*"
    ConstraintDescription: must begin with a letter and contain only alphanumeric characters.
    Default: dbadmin
  DBPassword:
    Description: The database admin account password
    NoEcho: true
    Type: String
    MinLength: 8
    MaxLength: 41
    AllowedPattern: "[a-zA-Z0-9]*"
    ConstraintDescription: must contain only alphanumeric characters.
  DBPort:
    Description: The port at which database will be running
    Type: Number
    Default: 3306
  DBInstanceClass:
    Type: String
    Description: The instance type to use for the database.
    Default: db.t3.medium
    AllowedValues:
      - db.t3.small
      - db.t3.medium
      - db.t3.large
      - db.r5.large
      - db.r5.xlarge
      - db.r5.2xlarge
      - db.r5.4xlarge
  DBType:
    Type: String
    Description: Type of aurora database (Currently only supports mysql in this template version)
    Default: mysql
    AllowedValues: [mysql] # Only mysql supported now with integrated parameter group
  Environment:
    Type: String
    AllowedValues: ["lab", "dev", "int", "uat", "prod"]
    Default: dev
    Description: The environment
    ConstraintDescription: Must be one of dev, int, uat or prod.
  ReplicaMinCapacity:
    Type: Number
    Description: Minimum number of read replicas required
    Default: 1
  ReplicaMaxCapacity:
    Type: Number
    Description: Maximum number of read replicas required
    Default: 5
  ReplicaCpuThreshold:
    Type: Number
    Description: CPU threshold for auto scaling replica
    Default: 60
  ReplicaScaleInCooldownInSeconds:
    Type: Number
    Description: Amount of time, in seconds, after a scale-in activity completes before another scale-in activity can start
    Default: 300
  ReplicaScaleOutCooldownInSeconds:
    Type: Number
    Description: Amount of time, in seconds, after a scale-out activity completes before another scale-out activity can start
    Default: 120
  KmsRdsKey:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-database
  NetworkStackName:
    Description: Name of the Network Stack. i.e 'AMRPWL-Dev/AMRPWL-Int/AMRPWL-UAT/AMRPWL-Load/AMRPWL-Prod'
    Type: String
  CpuUtilizationThreshold:
    Type: Number
    Description: Percentage of cpu utilization that is considered high for alarms
    Default: 90
  PagerDutyURL:
    Description: Pager Duty Integration Key for HTTPS Database Cloudwatch notifications
    Type: String

Mappings:
  Engine:
    mysql:
      engine: aurora-mysql
  EngineVersion:
    mysql:
      version: 8.0.37

Conditions:
  IsProd: !Equals [!Ref Environment, prod]
  IsDevEnvironment: !Equals [ !Ref 'Environment', 'dev' ]

Resources:
  RDSDBClusterParameterGroup:
    Type: 'AWS::RDS::DBClusterParameterGroup'
    Properties:
      Description: 'Custom parameter group for MySql 8.0'
      Family: aurora-mysql8.0
      Parameters:
        server_audit_logging: 'ON'
        server_audit_events: 'CONNECT,QUERY,TABLE'
        server_audit_logs_upload: '1'
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-cluster-parameter-group"

  ### Database Cluster Resources ###
  DatabaseSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub "Database Access for ${AWS::StackName}"
      VpcId:
        !ImportValue
          'Fn::Sub': "${NetworkStackName}-VpcId"
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-A-CIDR"
          Description: !Sub "Allow from App AZ-A (${NetworkStackName}-App-AZ-A-SubID)"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-B-CIDR"
          Description: !Sub "Allow from App AZ-B (${NetworkStackName}-App-AZ-B-SubID)"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
                'Fn::Sub': "${NetworkStackName}-App-AZ-C-CIDR"
          Description: !Sub "Allow from App AZ-C (${NetworkStackName}-App-AZ-C-SubID)"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp: !If [IsProd, "**********/32", "**********/32"]
          Description: "Bastion Hosts"
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-db-client-sg"

  DatabaseSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: !Sub "DB Subnet Group for ${AWS::StackName}"
      SubnetIds:
        - !ImportValue
            'Fn::Sub': "${NetworkStackName}-Data-AZ-D-SubID"
        - !ImportValue
            'Fn::Sub': "${NetworkStackName}-Data-AZ-E-SubID"
        - !ImportValue
            'Fn::Sub': "${NetworkStackName}-Data-AZ-F-SubID"
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-db-subnet-group"

  DatabaseCluster:
    Type: AWS::RDS::DBCluster
    Properties:
      BackupRetentionPeriod: !If [IsProd, 7, 1]
      DatabaseName: !Ref DBName
      DBClusterParameterGroupName: !Ref RDSDBClusterParameterGroup
      DBSubnetGroupName: !Ref DatabaseSubnetGroup
      Engine: !FindInMap [Engine, !Ref DBType, "engine"]
      EngineVersion: !FindInMap [EngineVersion, !Ref DBType, "version"]
      MasterUsername: !Ref DBUser
      MasterUserPassword: !Ref DBPassword
      Port: !Ref DBPort
      PreferredBackupWindow: 05:00-06:00
      PreferredMaintenanceWindow: sun:06:00-sun:07:00
      # We are not specifiying KmsKeyId because Cloudformation will use default aws/rds KMS key
      # ToDo - check with lead if its ok we are using default KMS key - and we do not want to create new key
      StorageEncrypted: true
      VpcSecurityGroupIds: [!Ref DatabaseSecurityGroup]
      KmsKeyId:
        Fn::ImportValue: !Ref KmsRdsKey
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-db-cluster"

  ## Initial Database Instances ##
  DatabaseWriterInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      CopyTagsToSnapshot: true
      DBClusterIdentifier: !Ref DatabaseCluster
      DBInstanceClass: !Ref DBInstanceClass
      DBInstanceIdentifier: !Sub ${AWS::StackName}-writer
      Engine: !FindInMap [Engine, !Ref DBType, "engine"]
      CACertificateIdentifier: rds-ca-rsa2048-g1

  # Even though we will have auto scaling - we want to create one reader instance to get started
  # additional readers will be added dynamically from auto scaling

  ## RDS Auto Scaling Policy ##
  # for RDS Auto scaling - look at this
  # https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Aurora.Integrating.AutoScaling.html
  RdsReplicaScalableTarget:
    # https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-applicationautoscaling-scalabletarget.html
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref ReplicaMaxCapacity
      MinCapacity: !Ref ReplicaMinCapacity
      ResourceId: !Sub cluster:${DatabaseCluster}
      RoleARN:
        # this is the service linked role for RDS Auto Scaling
        !Sub arn:aws:iam::${AWS::AccountId}:role/aws-service-role/rds.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_RDSCluster
      ScalableDimension: rds:cluster:ReadReplicaCount
      ServiceNamespace: rds

  RdsReplicaScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub "${AWS::StackName}-AuroraAutoScalingCpuPolicy"
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref RdsReplicaScalableTarget
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: !Ref ReplicaCpuThreshold
        ScaleInCooldown: !Ref ReplicaScaleInCooldownInSeconds
        ScaleOutCooldown: !Ref ReplicaScaleOutCooldownInSeconds
        PredefinedMetricSpecification:
          PredefinedMetricType: RDSReaderAverageCPUUtilization


  DBSNSTopic:
    Type: 'AWS::SNS::Topic'
    Properties:
      TopicName: !Sub '${DatabaseCluster}-SNS-Alarm'
      Subscription:
        - Endpoint: !Ref PagerDutyURL
          Protocol: https

  ## RDS Alarms ##
  DatabaseHighCPUUtilizationAlarm:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: !Sub "High CPU Utilization for Cluster ${DatabaseCluster}"
      MetricName: CPUUtilization
      Namespace: AWS/RDS
      Statistic: Maximum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 3
      Threshold: !Ref CpuUtilizationThreshold
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: breaching
      Dimensions:
        - Name: DBClusterIdentifier
          Value: !Ref DatabaseCluster

## Outputs ##
Outputs:
  DatabaseWriterEndpoint:
    Description: Cluster Writer Endpoint
    Value: !Sub "${DatabaseCluster.Endpoint.Address}:${DatabaseCluster.Endpoint.Port}"
