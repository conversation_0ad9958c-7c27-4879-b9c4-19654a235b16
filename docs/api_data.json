[{"type": "post", "url": "/groups/", "title": "Add a new Group", "name": "AddGroup", "group": "Group", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "object", "optional": false, "field": "result", "description": "<p>object containing group object</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n  {\n      \"id\": 200,\n      \"name\": \"Test\",\n      \"coversAllPartners\": false,\n      \"partnerMappings\": [\n          {\n              \"groupId\": 200,\n              \"partnerId\": \"b990f77a-6a27-47df-a373-8f227b32edc7\"\n          }\n      ],\n      \"partners\": []\n  }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "post", "url": "/groups/:id", "title": "Get Group by Id", "name": "GetGroupById", "group": "Group", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Group", "optional": false, "field": "result", "description": "<p>object containing group object</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n  {\n      \"id\": 200,\n      \"name\": \"Test\",\n      \"coversAllPartners\": false,\n      \"partnerMappings\": [\n          {\n              \"groupId\": 200,\n              \"partnerId\": \"b990f77a-6a27-47df-a373-8f227b32edc7\"\n          }\n      ],\n      \"partners\": []\n  }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "get", "url": "/groups/", "title": "Get all Groups and associated Partners in groups", "name": "GetGroups", "group": "Group", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "object", "optional": false, "field": "result", "description": "<p>object containing array of groups and partners</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n{\n  \"groups\": [],\n}", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "post", "url": "/groups/:groupId/partners", "title": "Add Partner to Group by ID", "name": "addPartnerToGroup", "group": "Group", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Group", "optional": false, "field": "result", "description": "<p>object containing group object</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n  {\n      \"id\": 200,\n      \"name\": \"Test\",\n      \"coversAllPartners\": false,\n      \"partnerMappings\": [\n          {\n              \"groupId\": 200,\n              \"partnerId\": \"b990f77a-6a27-47df-a373-8f227b32edc7\"\n          }\n      ],\n      \"partners\": []\n  }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "delete", "url": "/groups/:groupId/partners/:partnerId", "title": "Remove Partner from Group by ID", "name": "deletePartnerFromGroup", "group": "Group", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "empty", "description": "<p>response</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "get", "url": "/groups/:groupId/users", "title": "Get users by group id", "name": "getUsersByGroupId", "group": "Group", "success": {"examples": [{"title": "Success-Response:", "content": "    HTTP/1.1 200 OK\n\n[\n{\n    \"userId\": \"<EMAIL>\",\n    \"groupId\": 200,\n    \"createdDate\": null\n }\n]", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/GroupsController.ts", "groupTitle": "Group"}, {"type": "post", "url": "/offers/bulk/publish", "title": "", "name": "bulkPublishOffers", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "Successfully", "description": "<p>published offers by ids</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No Content", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "post", "url": "/offers/generate-content", "title": "", "name": "generateContent", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "Returns", "description": "<p>The generated copy text for an associated offer</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 No Content\n\n{\n \"awardLong\": [\n   {\n     \"en-US\": \"13 Bonus Miles\",\n     \"fr-CA\": \"13 milles en prime\"\n   }\n ],\n \"qualifierLong\": [\n   {\n     \"en-US\": \"Buy 24 13x the Miles*\",\n     \"fr-CA\": \"Achetez 24 13x les milles*\"\n   }\n ],\n \"awardShort\": {\n   \"en-US\": \"13 Bonus Miles\",\n   \"fr-CA\": \"13 milles en prime\"\n },\n \"qualifierShort\": {\n   \"en-US\": \"Buy 24 13x the Miles*\",\n   \"fr-CA\": \"Achetez 24 13x les milles*\"\n },\n \"legal\": {\n   \"en-US\": \"* Offer valid from December 5, 2018 to December 25, 2018. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. Please allow up to 271 days from the offer end date for Bonus Miles to be posted to your Collector Account. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by LoyaltyOne, Co. and Partner Legal Name. Trademark Information\",\n   \"fr-CA\": \"* Offre en vigueur du 5 décembre 2018 au 25 décembre 2018. L’achat minimum admissible doit être effectué en une seule transaction. Jusqu’à épuisement des stocks. La disponibilité des produits peut varier selon le magasin. Nous nous réservons le droit d’appliquer une limite aux quantités. Veuillez prévoir jusqu’à 271 jours après la fin de l’offre pour le versement des milles en prime dans votre compte d’adhérent. La carte AIR MILES doit être présentée au moment de l’achat. Peut se combiner avec d’autres offres et offres AIR MILES. md/mc Marque déposée/de commerce d'AM Royalties Limited Partnership, employée en vertu d'une licence par LoyaltyOne, Co. et par Partner Legal Name. Trademark Information\"\n }\n}", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "get", "url": "/offers/jobs", "title": "Gaet bulk jobs for a user", "name": "getBulkJobs", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "Array", "description": "<p>of all BulkJobs the user has access to</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n[\n  {\n    \"id\": \"ebee8ad2-99b7-42c8-86ff-f81025661112\",\n    \"name\": \"Feb bulk Upload 8578\",\n    \"createdBy\": \"<EMAIL>\",\n    \"createdAt\": \"2019-03-08T15:58:57Z\",\n    \"partnerId\": \"82bc8b9e-8b19-41d9-a9f8-2bff89bc76f9\",\n    \"partnerName\": \"LCBO\",\n    \"totalOffers\": 90\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "get", "url": "/offers", "title": "Get offers for a user by filters", "name": "getOffersByFilters", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": true, "field": "Offer", "description": "<p>Array of all Offers the user has access to</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n[\n    {\n        \"regions\": [\n            \"AB\",\n            \"BC\",\n            \"MB\",\n            \"NT\",\n        \"ON\",\n        \"SK\"\n    ],\n    \"hasCustomLegal\": false,\n    \"endDate\": \"2019-02-22T23:59:00Z\",\n    \"awardType\": \"cashDiscount\",\n    \"availability\": [\n        \"inStore\"\n    ],\n    \"canBeCombined\": true,\n    \"image\": {\n        \"en-US\": {\n            \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/amCash.png\"\n        }\n    },\n    \"mechanisms\": [\n        {\n            \"mechanismType\": \"noAction\"\n        }\n    ],\n    \"partnerName\": \"Rexall\",\n    \"offerLimitation\": \"noLimit\",\n    \"startDate\": \"2019-02-22T00:00:00Z\",\n    \"partnerId\": \"f0f6e127-e5c1-44e2-9991-d105b482e663\",\n    \"offerType\": \"amCashDiscount\",\n    \"qualifier\": \"cashDiscount\",\n    \"displayDate\": \"2019-02-22T00:00:00Z\",\n    \"tiers\": [\n        {\n            \"awardValue\": 20,\n            \"qualifierValue\": 75\n        }\n    ],\n    \"baseCashRedemption\": 95,\n    \"displayPriority\": 0\n }\n]", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "get", "url": "/offers/counts", "title": "Get offers counts", "name": "getOffersCounts", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": true, "field": "Offer", "description": "<p>Array of all Offers the user has access to</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n {\n  \"draft\": 95,\n    \"changesPending\": 0\n  \"staged\": 95,\n    \"expired\": 0\n   \"total\": 95,\n }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "post", "url": "/offers/publish", "title": "", "name": "publishOffer", "group": "Offers", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "Successfully", "description": "<p>published offers by id</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No Content", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/OffersController.ts", "groupTitle": "Offers"}, {"type": "post", "url": "/partners/", "title": "Get all Partners", "name": "onGetPartners", "group": "Partner", "success": {"fields": {"Success 200": [{"group": "Success 200", "optional": false, "field": "Array", "description": "<p>of all partners</p>"}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n [{\n     \"name\": \"<PERSON><PERSON>\",\n      \"sponsorCodes\": [\"RXAL\"],\n      \"type\": [\"in-store\", \"cash\", \"load+go\"],\n     \"fullLogo\": [{\n       \"title\": \"@2x\",\n       \"file\": {\n         \"url\": \"//images.ctfassets.net/hpbflulg5svm/MRgY4ZtEcgc6iy0M6GEkO/739b6e7f59a6e395511e93964c1c1032/Rexall_Drugstore.png\",\n         \"details\": {\n           \"size\": 18339,\n           \"image\": {\n             \"width\": 512,\n            \"height\": 512\n          }\n        },\n        \"fileName\": \"Rexall Drugstore.png\",\n        \"contentType\": \"image/png\"\n       }\n    }],\n    \"baseEarnRate\": \"1 Mile for every $20 spent per transaction\",\n     \"maxCashMiles\": \"Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day.\",\n     \"regions\": [\"ab\", \"bc\", \"mb\", \"nt\", \"on\", \"sk\"],\n }]", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/PartnersController.ts", "groupTitle": "Partner"}, {"type": "post", "url": "/users/:userId/groups", "title": "Add Group to User by ID", "name": "addGroupToUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n  {\n      \"groupId\": \"groupId\",\n      \"userId\": \"userId\"\n  }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}, {"type": "post", "url": "/users/:userId/partners", "title": "Add Partner to User by ID", "name": "addPartnerToUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n  {\n      \"partnerId\": \"partnerId\",\n      \"userId\": \"userId\"\n  }", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}, {"type": "get", "url": "/users/:userId/groups", "title": "Get Groups for User", "name": "onGetGroupsForUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "    HTTP/1.1 200 OK\n      [{\n    \"id\": 201,\n    \"name\": \"Group2\",\n    \"coversAllPartners\": false,\n    \"updatedDate\": \"2019-01-29T22:17:55.449Z\",\n    \"version\": 1,\n    \"createdBy\": null,\n    \"updatedBy\": null,\n    \"users\": [\n        {\n            \"userId\": \"<EMAIL>\",\n            \"groupId\": 201,\n            \"createdDate\": \"2019-02-06T22:34:37.630Z\"\n        }\n    ],\n    \"partnerMappings\": [\n        {\n            \"groupId\": 201,\n            \"partnerId\": \"4a755252-876b-478e-9440-42961525e307\"\n        }\n    ],\n    \"partners\": [\n        {\n            \"name\": \"Metro\",\n            \"sponsorCodes\": [\n                \"APAP\",\n                \"APAT\",\n                \"APDO\",\n                \"APUL\",\n                \"BARN\"\n            ],\n            \"type\": [\n                \"in-store\",\n                \"cash\"\n            ],\n            \"fullLogo\": [\n                {\n                    \"title\": \"@2x\",\n                    \"file\": {\n                        \"url\": \"//images.ctfassets.net/hpbflulg5svm/7HeIncCJX2qCQcSakWmSEo/a4dfc2d8e2410ad2643ad732f7bbe69e/Metro.png\",\n                        \"details\": {\n                            \"size\": 20117,\n                            \"image\": {\n                                \"width\": 512,\n                                \"height\": 512\n                            }\n                        },\n                        \"fileName\": \"Metro.png\",\n                        \"contentType\": \"image/png\"\n                    }\n                }\n            ],\n            \"baseEarnRate\": \"1 Mile for every $20 spent in a week (Sunday to Saturday) Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo\",\n            \"maxCashMiles\": \"Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day. Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo\",\n            \"regions\": [\n                \"on\"\n            ],\n            \"priority\": 10,\n            \"id\": \"4a755252-876b-478e-9440-42961525e307\",\n            \"createdAt\": \"2017-05-17T13:18:34.403Z\",\n            \"updatedAt\": \"2018-09-24T17:45:57.250Z\",\n            \"revision\": 8\n        }]\n}]", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}, {"type": "get", "url": "/users/:userId/partners", "title": "Get Partners for User by ID", "name": "onGetPartnersForUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "    HTTP/1.1 200 OK\n     {\n    \"name\": \"Metro\",\n    \"sponsorCodes\": [\n        \"APAP\",\n        \"APAT\",\n        \"APDO\",\n        \"APUL\",\n        \"BARN\"\n    ],\n    \"type\": [\n        \"in-store\",\n        \"cash\"\n    ],\n    \"fullLogo\": [\n        {\n            \"title\": \"@2x\",\n            \"file\": {\n                \"url\": \"//images.ctfassets.net/hpbflulg5svm/7HeIncCJX2qCQcSakWmSEo/a4dfc2d8e2410ad2643ad732f7bbe69e/Metro.png\",\n                \"details\": {\n                    \"size\": 20117,\n                    \"image\": {\n                        \"width\": 512,\n                        \"height\": 512\n                    }\n                },\n                \"fileName\": \"Metro.png\",\n                \"contentType\": \"image/png\"\n            }\n        }\n    ],\n    \"baseEarnRate\": \"1 Mile for every $20 spent in a week (Sunday to Saturday) Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo\",\n    \"maxCashMiles\": \"Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day. Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo\",\n    \"regions\": [\n        \"on\"\n    ],\n    \"priority\": 10,\n    \"id\": \"4a755252-876b-478e-9440-42961525e307\",\n    \"createdAt\": \"2017-05-17T13:18:34.403Z\",\n    \"updatedAt\": \"2018-09-24T17:45:57.250Z\",\n    \"revision\": 8\n}", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}, {"type": "delete", "url": "/users/:userId/groups/:groupId", "title": "Remove group from user", "name": "removeGroupFromUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}, {"type": "delete", "url": "/users/:userId/partners/:partnerId", "title": "Remove partner from user", "name": "removePartnerFromUser", "group": "Users", "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK", "type": "json"}]}, "error": {"fields": {"Error 4xx": [{"group": "Error 4xx", "optional": false, "description": ""}]}}, "version": "0.0.0", "filename": "./src/controllers/UsersController.ts", "groupTitle": "Users"}]