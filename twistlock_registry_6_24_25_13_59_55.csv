Registry                                     ,Repository            ,Tag     ,Id                                                                         ,Distro        ,Hostname                    ,Layer                                                                                                                                                                                                  ,CVE ID              ,Compliance ID ,Result ,Type   ,Severity ,Packages             ,Source Package ,Package Version ,Package License           ,CVSS ,Fix Status                            ,Risk Factors                                                                                                   ,Vulnerability Tags ,Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,Cause ,Custom Labels ,Published               ,Namespace ,Image ID                                                                ,Vulnerability Link                                                                                   ,Package Path                                                                                                                                                                                     ,PURL
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-28155      ,           49 ,fail   ,nodejs ,medium   ,request              ,               ,2.88.0          ,Apache-2.0                ,6.10 ,                                      ,"Attack complexity: low, Attack vector: network, Medium severity"                                              ,                   ,"The Request package through 2.88.1 for Node.js allows a bypass of SSRF mitigations via an attacker-controller server that does a cross-protocol redirect (HTTP to HTTPS, or HTTPS to HTTP). NOTE: This vulnerability only affects products that are no longer supported by the maintainer."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-03-16 15:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-28155                                                      ,/opt/app/node_modules/request                                                                                                                                                                    ,pkg:npm/request@2.88.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7788       ,           49 ,fail   ,nodejs ,critical ,ini                  ,               ,1.3.4           ,ISC                       ,9.80 ,fixed in 1.3.6                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"This affects the package ini before 1.3.6. If an attacker submits a malicious INI file to an application that parses it with ini.parse, they will pollute the prototype on the application. This can be exploited further depending on the context."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2020-12-11 11:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7788                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/ini                                                                                                                                      ,pkg:npm/ini@1.3.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25883      ,           49 ,fail   ,nodejs ,high     ,semver               ,               ,5.7.0           ,ISC                       ,7.50 ,"fixed in 7.5.2, 6.3.1, 5.7.2"        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package semver before 7.5.2 are vulnerable to Regular Expression Denial of Service (ReDoS) via the function new Range, when untrusted user data is provided as a range.\r\r\r"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2023-06-21 05:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25883                                                      ,/opt/app/node_modules/semver                                                                                                                                                                     ,pkg:npm/semver@5.7.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-21538      ,           49 ,fail   ,nodejs ,high     ,cross-spawn          ,               ,5.1.0           ,MIT                       ,7.50 ,"fixed in 7.0.5, 6.0.6"               ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package cross-spawn before 6.0.6, from 7.0.0 and before 7.0.5 are vulnerable to Regular Expression Denial of Service (ReDoS) due to improper input sanitization. An attacker can increase the CPU usage and crash the program by crafting a very large and well crafted string."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2024-11-08 06:30:47.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-21538                                                      ,/opt/app/node_modules/npx/node_modules/cross-spawn                                                                                                                                               ,pkg:npm/cross-spawn@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-xgh6-85xh-479p ,           49 ,fail   ,nodejs ,low      ,npm-user-validate    ,               ,1.0.0           ,BSD-2-Clause              ,1.00 ,fixed in 1.0.1                        ,Has fix                                                                                                        ,                   ,"`npm-user-validate` before version `1.0.1` is vulnerable to a Regular Expression Denial of Service (REDos). The regex that validates user emails took exponentially longer to process long input strings beginning with `@` characters.  ### Impact The issue affects the `email` function. If you use this function to process arbitrary user input with no character limit the application may be susceptible to Denial of Service.  ### Patches The issue is patched in version 1.0.1 by improving the regular expression used and also enforcing a 254 character limit.  ### Workarounds Restrict the character length to a reasonable degree before passing a value to `.emal()`; Also, consider doing a more rigorous sanitizing/validation beforehand."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2020-10-16 18:56:26.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-xgh6-85xh-479p                                                    ,/opt/app/node_modules/npx/node_modules/npm/node_modules/npm-user-validate                                                                                                                        ,pkg:npm/npm-user-validate@1.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7754       ,           49 ,fail   ,nodejs ,medium   ,npm-user-validate    ,               ,1.0.0           ,BSD-2-Clause              ,5.00 ,fixed in 1.0.1                        ,"Attack complexity: low, Attack vector: network, Has fix, Medium severity"                                     ,                   ,This affects the package npm-user-validate before 1.0.1. The regex that validates user emails took exponentially longer to process long input strings beginning with @ characters.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,      ,              ,2020-10-27 15:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7754                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/npm-user-validate                                                                                                                        ,pkg:npm/npm-user-validate@1.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-28503      ,           49 ,fail   ,nodejs ,critical ,copy-props           ,               ,2.0.4           ,MIT                       ,9.80 ,fixed in 2.0.5                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,The package copy-props before 2.0.5 are vulnerable to Prototype Pollution via the main functionality.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2021-03-23 10:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-28503                                                      ,/opt/app/node_modules/copy-props                                                                                                                                                                 ,pkg:npm/copy-props@2.0.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-33987      ,           49 ,fail   ,nodejs ,medium   ,got                  ,               ,6.7.1           ,MIT                       ,5.30 ,fixed in 11.8.5                       ,"Attack complexity: low, Attack vector: network, Has fix, Medium severity"                                     ,                   ,The got package before 12.1.0 (also fixed in 11.8.5) for Node.js allows a redirect to a UNIX socket.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2022-06-18 21:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-33987                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/got                                                   ,pkg:npm/got@6.7.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0049    ,           49 ,fail   ,nodejs ,high     ,unset-value          ,               ,1.0.0           ,MIT                       ,7.50 ,fixed in 2.0.1                        ,"Has fix, High severity, Remote execution"                                                                     ,                   ,"unset-value package versions before 2.0.1 are vulnerable to Prototype Pollution. unset() function in index.js files allows for access to object prototype properties. An attacker can exploit this to override the behavior of object prototypes, resulting in a possible Denial of Service (DoS), Remote Code Execution (RCE), or other unexpected behavior."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2022-02-21 10:09:35.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/jonschlinkert/unset-value/pull/12/commits/abb534769f6ea62c3dd988f5ce0a4ebd1f91b56 ,/opt/app/node_modules/unset-value                                                                                                                                                                ,pkg:npm/unset-value@1.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-20835      ,           49 ,fail   ,nodejs ,high     ,tar-fs               ,               ,1.15.3          ,MIT                       ,7.50 ,fixed in 1.16.2                       ,"Attack complexity: low, Attack vector: network, Exploit exists - POC, Has fix, High severity"                 ,                   ,"A vulnerability was found in tar-fs before 1.16.2. An Arbitrary File Overwrite issue exists when extracting a tarball containing a hardlink to a file that already exists on the system, in conjunction with a later plain file with the same name as the hardlink. This plain file content replaces the existing file content."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2019-04-30 19:29:03.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-20835                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs                                                                                                               ,pkg:npm/tar-fs@1.15.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-12905      ,           49 ,fail   ,nodejs ,high     ,tar-fs               ,               ,1.15.3          ,MIT                       ,7.50 ,"fixed in 3.0.7, 2.1.2, 1.16.4"       ,"Has fix, High severity, Recent vulnerability"                                                                 ,                   ,"An Improper Link Resolution Before File Access (\""Link Following\"") and Improper Limitation of a Pathname to a Restricted Directory (\""Path Traversal\""). This vulnerability occurs when extracting a maliciously crafted tar file, which can result in unauthorized file writes or overwrites outside the intended extraction directory. The issue is associated with index.js in the tar-fs package.  This issue affects tar-fs: from 0.0.0 before 1.16.4, from 2.0.0 before 2.1.2, from 3.0.0 before 3.0.8."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2025-03-27 17:15:53.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-12905                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs                                                                                                               ,pkg:npm/tar-fs@1.15.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2025-48387      ,           49 ,fail   ,nodejs ,high     ,tar-fs               ,               ,1.15.3          ,MIT                       ,8.70 ,"fixed in 3.0.9, 2.1.3, 1.16.5"       ,"Has fix, High severity, Recent vulnerability"                                                                 ,                   ,"tar-fs provides filesystem bindings for tar-stream. Versions prior to 3.0.9, 2.1.3, and 1.16.5 have an issue where an extract can write outside the specified dir with a specific tarball. This has been patched in versions 3.0.9, 2.1.3, and 1.16.5. As a workaround, use the ignore option to ignore non files/directories."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2025-06-02 20:15:22.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2025-48387                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-fs                                                                                                               ,pkg:npm/tar-fs@1.15.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-27088      ,           49 ,fail   ,nodejs ,medium   ,es5-ext              ,               ,0.10.49         ,ISC                       ,5.50 ,fixed in 0.10.63                      ,"Attack complexity: low, DoS - High, Has fix, Medium severity"                                                 ,                   ,es5-ext contains ECMAScript 5 extensions. Passing functions with very long names or complex default argument names into `function#copy` or `function#toStringTokens` may cause the script to stall. The vulnerability is patched in v0.10.63.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,      ,              ,2024-02-26 17:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-27088                                                      ,/opt/app/node_modules/es5-ext                                                                                                                                                                    ,pkg:npm/es5-ext@0.10.49
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-3807       ,           49 ,fail   ,nodejs ,high     ,ansi-regex           ,               ,3.0.0           ,MIT                       ,7.50 ,fixed in 4.1.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,ansi-regex is vulnerable to Inefficient Regular Expression Complexity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2021-09-17 07:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-3807                                                       ,/opt/app/node_modules/string-width/node_modules/ansi-regex                                                                                                                                       ,pkg:npm/ansi-regex@3.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-26136      ,           49 ,fail   ,nodejs ,critical ,tough-cookie         ,               ,2.3.2           ,BSD-3-Clause              ,9.80 ,fixed in 4.1.3                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-07-01 05:15:16.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-26136                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/tough-cookie                                                                                                        ,pkg:npm/tough-cookie@2.3.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2017-15010      ,           49 ,fail   ,nodejs ,high     ,tough-cookie         ,               ,2.3.2           ,BSD-3-Clause              ,7.50 ,fixed in 2.3.3                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,A ReDoS (regular expression denial of service) flaw was found in the tough-cookie module before 2.3.3 for Node.js. An attacker that is able to make an HTTP request using a specially crafted cookie may cause the application to consume an excessive amount of CPU.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2017-10-04 01:29:03.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2017-15010                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/tough-cookie                                                                                                        ,pkg:npm/tough-cookie@2.3.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23337      ,           49 ,fail   ,nodejs ,high     ,lodash.template      ,               ,3.6.2           ,MIT                       ,7.20 ,open                                  ,"Attack complexity: low, Attack vector: network, DoS - High, High severity"                                    ,                   ,Lodash versions prior to 4.17.21 are vulnerable to Command Injection via the template function.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-02-15 13:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23337                                                      ,/opt/app/node_modules/lodash.template                                                                                                                                                            ,pkg:npm/lodash.template@3.6.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-24999      ,           49 ,fail   ,nodejs ,high     ,qs                   ,               ,6.7.0           ,BSD-3-Clause              ,7.50 ,"fixed in 6.10.3, 6.9.7, 6.8.3,..."   ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,"qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \""deps: qs@6.9.7\"" in its release description, is not vulnerable)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2022-11-26 22:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-24999                                                      ,/opt/app/node_modules/qs                                                                                                                                                                         ,pkg:npm/qs@6.7.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2025-5889       ,           49 ,fail   ,nodejs ,low      ,brace-expansion      ,               ,1.1.8           ,MIT                       ,3.10 ,"fixed in 4.0.1, 3.0.1, 2.0.2,..."    ,"Has fix, Recent vulnerability"                                                                                ,                   ,"A vulnerability was found in juliangruber brace-expansion up to 1.1.11/2.0.1/3.0.0/4.0.0. It has been rated as problematic. Affected by this issue is the function expand of the file index.js. The manipulation leads to inefficient regular expression complexity. The attack may be launched remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. Upgrading to version 1.1.12, 2.0.2, 3.0.1 and 4.0.1 is able to address this issue. The name of the patch is a5b98a4f30d7813266b221435e1eaaf25a1b0ac5. It is recommended to upgrade the affected component."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2025-06-09 19:15:25.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2025-5889                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch/node_modules/brace-expansion                                                                               ,pkg:npm/brace-expansion@1.1.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8116       ,           49 ,fail   ,nodejs ,high     ,dot-prop             ,               ,4.2.0           ,MIT                       ,7.30 ,"fixed in 5.1.1, 4.2.1"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Exploit exists - POC, Has fix, High severity"      ,                   ,Prototype pollution vulnerability in dot-prop npm package versions before 4.2.1 and versions 5.x before 5.1.1 allows an attacker to add arbitrary properties to JavaScript language constructs such as objects.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2020-02-04 20:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8116                                                       ,/opt/app/node_modules/npx/node_modules/dot-prop                                                                                                                                                  ,pkg:npm/dot-prop@4.2.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8116       ,           49 ,fail   ,nodejs ,high     ,dot-prop             ,               ,4.1.1           ,MIT                       ,7.30 ,"fixed in 5.1.1, 4.2.1"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Exploit exists - POC, Has fix, High severity"      ,                   ,Prototype pollution vulnerability in dot-prop npm package versions before 4.2.1 and versions 5.x before 5.1.1 allows an attacker to add arbitrary properties to JavaScript language constructs such as objects.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2020-02-04 20:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8116                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/configstore/node_modules/dot-prop                                                                           ,pkg:npm/dot-prop@4.1.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7751       ,           49 ,fail   ,nodejs ,high     ,pathval              ,               ,1.1.0           ,MIT                       ,7.20 ,fixed in 1.1.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,pathval before version 1.1.1 is vulnerable to prototype pollution.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,      ,              ,2020-10-26 12:17:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7751                                                       ,/opt/app/node_modules/pathval                                                                                                                                                                    ,pkg:npm/pathval@1.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-3517       ,           49 ,fail   ,nodejs ,high     ,minimatch            ,               ,3.0.4           ,ISC                       ,7.50 ,fixed in 3.0.5                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"A vulnerability was found in the minimatch package. This flaw allows a Regular Expression Denial of Service (ReDoS) when calling the braceExpand function with specific arguments, resulting in a Denial of Service."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2022-10-17 20:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-3517                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/minimatch                                                                                                            ,pkg:npm/minimatch@3.0.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-1010266    ,           49 ,fail   ,nodejs ,medium   ,lodash               ,               ,3.10.1          ,MIT                       ,6.50 ,fixed in 4.17.11                      ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, Medium severity"                         ,                   ,"lodash prior to 4.17.11 is affected by: CWE-400: Uncontrolled Resource Consumption. The impact is: Denial of service. The component is: Date handler. The attack vector is: Attacker provides very long strings, which the library attempts to match using a regular expression. The fixed version is: 4.17.11."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2019-07-17 21:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-1010266                                                    ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-16487      ,           49 ,fail   ,nodejs ,medium   ,lodash               ,               ,3.10.1          ,MIT                       ,5.60 ,fixed in 4.17.11                      ,"Attack vector: network, DoS - Low, Exploit exists - POC, Has fix, Medium severity"                            ,                   ,"A prototype pollution vulnerability was found in lodash <4.17.11 where the functions merge, mergeWith, and defaultsDeep can be tricked into adding or modifying properties of Object.prototype."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2019-02-01 18:29:00.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-16487                                                      ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-3721       ,           49 ,fail   ,nodejs ,medium   ,lodash               ,               ,3.10.1          ,MIT                       ,6.50 ,fixed in 4.17.5                       ,"Attack complexity: low, Attack vector: network, Exploit exists - POC, Has fix, Medium severity"               ,                   ,"lodash node module before 4.17.5 suffers from a Modification of Assumed-Immutable Data (MAID) vulnerability via defaultsDeep, merge, and mergeWith functions, which allows a malicious user to modify the prototype of \""Object\"" via __proto__, causing the addition or modification of an existing property that will exist on all objects."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2018-06-07 02:29:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-3721                                                       ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10744      ,           49 ,fail   ,nodejs ,critical ,lodash               ,               ,3.10.1          ,MIT                       ,9.10 ,fixed in 4.17.12                      ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of lodash lower than 4.17.12 are vulnerable to Prototype Pollution. The function defaultsDeep could be tricked into adding or modifying properties of Object.prototype using a constructor payload.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2019-07-26 00:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10744                                                      ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23337      ,           49 ,fail   ,nodejs ,high     ,lodash               ,               ,3.10.1          ,MIT                       ,7.20 ,fixed in 4.17.21                      ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,Lodash versions prior to 4.17.21 are vulnerable to Command Injection via the template function.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-02-15 13:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23337                                                      ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8203       ,           49 ,fail   ,nodejs ,high     ,lodash               ,               ,3.10.1          ,MIT                       ,7.40 ,fixed in 4.17.20                      ,"Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"                             ,                   ,Prototype pollution attack when using _.zipObjectDeep in lodash before 4.17.20.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2020-07-15 17:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8203                                                       ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-28500      ,           49 ,fail   ,nodejs ,medium   ,lodash               ,               ,3.10.1          ,MIT                       ,5.30 ,fixed in 4.17.21                      ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,"Lodash versions prior to 4.17.21 are vulnerable to Regular Expression Denial of Service (ReDoS) via the toNumber, trim and trimEnd functions."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-02-15 11:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-28500                                                      ,/opt/app/node_modules/restify-health-router/node_modules/lodash                                                                                                                                  ,pkg:npm/lodash@3.10.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7608       ,           49 ,fail   ,nodejs ,medium   ,yargs-parser         ,               ,5.0.0           ,ISC                       ,5.30 ,"fixed in 18.1.1, 15.0.1, 13.1.2,..." ,"Attack complexity: low, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"yargs-parser could be tricked into adding or modifying properties of Object.prototype using a \""__proto__\"" payload."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2020-03-16 20:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7608                                                       ,/opt/app/node_modules/gulp-cli/node_modules/yargs-parser                                                                                                                                         ,pkg:npm/yargs-parser@5.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-42282      ,           49 ,fail   ,nodejs ,critical ,ip                   ,               ,1.1.5           ,MIT                       ,9.80 ,"fixed in 1.1.9, 2.0.1"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,The ip package before 1.1.9 for Node.js might allow SSRF because some IP addresses (such as 0x7f.1) are improperly categorized as globally routable via isPublic.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2024-02-08 17:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-42282                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/socks/node_modules/ip                                  ,pkg:npm/ip@1.1.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-29415      ,           49 ,fail   ,nodejs ,high     ,ip                   ,               ,1.1.5           ,MIT                       ,8.10 ,open                                  ,High severity                                                                                                  ,                   ,"The ip package through 2.0.1 for Node.js might allow SSRF because some IP addresses (such as 127.1, 01200034567, *********, 000:0:0000::01, and ::fFFf:127.0.0.1) are improperly categorized as globally routable via isPublic. NOTE: this issue exists because of an incomplete fix for CVE-2023-42282."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2024-05-27 20:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-29415                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/socks-proxy-agent/node_modules/socks/node_modules/ip                                  ,pkg:npm/ip@1.1.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2017-20165      ,           49 ,fail   ,nodejs ,high     ,debug                ,               ,2.6.8           ,MIT                       ,7.50 ,"fixed in 3.1.0, 2.6.9"               ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,A vulnerability classified as problematic has been found in debug-js debug up to 3.0.x. This affects the function useColors of the file src/node.js. The manipulation of the argument str leads to inefficient regular expression complexity. Upgrading to version 3.1.0 is able to address this issue. The identifier of the patch is c38a0166c266a679c8de012d4eaccec3f944e685. It is recommended to upgrade the affected component. The identifier VDB-217665 was assigned to this vulnerability.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2023-01-09 10:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2017-20165                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/debug                                                  ,pkg:npm/debug@2.6.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2017-16137      ,           49 ,fail   ,nodejs ,medium   ,debug                ,               ,2.6.8           ,MIT                       ,5.30 ,"fixed in 3.1.0, 2.6.9"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,The debug module is vulnerable to regular expression denial of service when untrusted user input is passed into the o formatter. It takes around 50k characters to block for 2 seconds making this a low severity issue.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2018-06-07 02:29:03.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2017-16137                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent/node_modules/debug                                                  ,pkg:npm/debug@2.6.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-3739       ,           49 ,fail   ,nodejs ,critical ,https-proxy-agent    ,               ,2.0.0           ,MIT                       ,9.10 ,fixed in 2.2.0                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,"https-proxy-agent before 2.1.1 passes auth option to the Buffer constructor without proper sanitization, resulting in DoS and uninitialized memory leak in setups where an attacker could submit typed input to the \'auth\' parameter (e.g. JSON)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2018-06-07 02:29:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-3739                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent                                                                     ,pkg:npm/https-proxy-agent@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-pc5p-h8pf-mvwp ,           49 ,fail   ,nodejs ,medium   ,https-proxy-agent    ,               ,2.0.0           ,MIT                       ,6.10 ,fixed in 2.2.3                        ,"Has fix, Medium severity"                                                                                     ,                   ,"Versions of `https-proxy-agent` prior to 2.2.3 are vulnerable to Machine-In-The-Middle. The package fails to enforce TLS on the socket if the proxy server responds the to the request with a HTTP status different than 200. This allows an attacker with access to the proxy server to intercept unencrypted communications, which may include sensitive information such as credentials.   ## Recommendation  Upgrade to version 3.0.0 or 2.2.3."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2020-04-16 03:14:56.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-pc5p-h8pf-mvwp                                                    ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/https-proxy-agent                                                                     ,pkg:npm/https-proxy-agent@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8244       ,           49 ,fail   ,nodejs ,medium   ,bl                   ,               ,1.2.1           ,MIT                       ,6.50 ,"fixed in 4.0.3, 3.0.1, 2.2.1,..."    ,"Attack complexity: low, Attack vector: network, DoS - Low, Exploit exists - POC, Has fix, Medium severity"    ,                   ,"A buffer over-read vulnerability exists in bl <4.0.3, <3.0.1, <2.2.1, and <1.2.3 which could allow an attacker to supply user input (even typed) that if it ends up in consume() argument and can become negative, the BufferList state can be corrupted, tricking it into exposing uninitialized memory via regular .slice() calls."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2020-08-30 15:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8244                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/tar-stream/node_modules/bl                                                                                           ,pkg:npm/bl@1.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7774       ,           49 ,fail   ,nodejs ,critical ,y18n                 ,               ,4.0.0           ,ISC                       ,9.80 ,"fixed in 5.0.5, 3.2.2"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"The package y18n before 3.2.2, 4.0.1 and 5.0.5, is vulnerable to Prototype Pollution."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2020-11-17 13:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7774                                                       ,/opt/app/node_modules/typeorm/node_modules/y18n                                                                                                                                                  ,pkg:npm/y18n@4.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-13173      ,           49 ,fail   ,nodejs ,high     ,fstream              ,               ,1.0.11          ,ISC                       ,7.50 ,fixed in 1.0.12                       ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,"fstream before 1.0.12 is vulnerable to Arbitrary File Overwrite. Extracting tarballs containing a hardlink to a file that already exists in the system, and a file that matches the hardlink, will overwrite the system\'s file with the contents of the extracted file. The fstream.DirWriter() function is vulnerable."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2019-07-02 20:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-13173                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/fstream                                                                                                                                  ,pkg:npm/fstream@1.0.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23362      ,           49 ,fail   ,nodejs ,medium   ,hosted-git-info      ,               ,2.6.0           ,ISC                       ,5.30 ,"fixed in 3.0.8, 2.8.9"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,The package hosted-git-info before 3.0.8 are vulnerable to Regular Expression Denial of Service (ReDoS) via regular expression shortcutMatch in the fromUrl function in index.js. The affected regular expression exhibits polynomial worst-case time complexity.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2021-03-23 17:15:14.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23362                                                      ,/opt/app/node_modules/npx/node_modules/hosted-git-info                                                                                                                                           ,pkg:npm/hosted-git-info@2.6.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-15095      ,           49 ,fail   ,nodejs ,medium   ,npm                  ,               ,5.1.0           ,Artistic-2.0              ,4.40 ,fixed in 6.14.6                       ,"Has fix, Medium severity"                                                                                     ,                   ,"Versions of the npm CLI prior to 6.14.6 are vulnerable to an information exposure vulnerability through log files. The CLI supports URLs like \""<protocol>://[<user>[:<password>]@]<hostname>[:<port>][:][/]<path>\"". The password value is not redacted and is printed to stdout and also to any generated log files."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2020-07-07 19:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-15095                                                      ,/opt/app/node_modules/npx/node_modules/npm                                                                                                                                                       ,pkg:npm/npm@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-7408       ,           49 ,fail   ,nodejs ,high     ,npm                  ,               ,5.1.0           ,Artistic-2.0              ,7.80 ,fixed in 5.7.1                        ,"Attack complexity: low, DoS - High, Has fix, High severity"                                                   ,                   ,"An issue was discovered in an npm 5.7.0 2018-02-21 pre-release (marked as \""next: 5.7.0\"" and therefore automatically installed by an \""npm upgrade -g npm\"" command, and also announced in the vendor\'s blog without mention of pre-release status). It might allow local users to bypass intended filesystem access restrictions because ownerships of /etc and /usr directories are being changed unexpectedly, related to a \""correctMkdir\"" issue."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2018-02-22 18:29:00.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-7408                                                       ,/opt/app/node_modules/npx/node_modules/npm                                                                                                                                                       ,pkg:npm/npm@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-16777      ,           49 ,fail   ,nodejs ,high     ,npm                  ,               ,5.1.0           ,Artistic-2.0              ,7.70 ,fixed in 6.13.4                       ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,"Versions of the npm CLI prior to 6.13.4 are vulnerable to an Arbitrary File Overwrite. It fails to prevent existing globally-installed binaries to be overwritten by other package installations. For example, if a package was installed globally and created a serve binary, any subsequent installs of packages that also create a serve binary would overwrite the previous serve binary. This behavior is still allowed in local installations and also through install scripts. This vulnerability bypasses a user using the --ignore-scripts install option."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2019-12-13 01:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-16777                                                      ,/opt/app/node_modules/npx/node_modules/npm                                                                                                                                                       ,pkg:npm/npm@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-16775      ,           49 ,fail   ,nodejs ,high     ,npm                  ,               ,5.1.0           ,Artistic-2.0              ,7.70 ,fixed in 6.13.3                       ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,Versions of the npm CLI prior to 6.13.3 are vulnerable to an Arbitrary File Write. It is possible for packages to create symlinks to files outside of thenode_modules folder through the bin field upon installation. A properly constructed entry in the package.json bin field would allow a package publisher to create a symlink pointing to arbitrary files on a user\'s system when the package is installed. This behavior is still possible through install scripts. This vulnerability bypasses a user using the --ignore-scripts install option.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2019-12-13 01:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-16775                                                      ,/opt/app/node_modules/npx/node_modules/npm                                                                                                                                                       ,pkg:npm/npm@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-16776      ,           49 ,fail   ,nodejs ,high     ,npm                  ,               ,5.1.0           ,Artistic-2.0              ,7.70 ,fixed in 6.13.3                       ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,Versions of the npm CLI prior to 6.13.3 are vulnerable to an Arbitrary File Write. It fails to prevent access to folders outside of the intended node_modules folder through the bin field. A properly constructed entry in the package.json bin field would allow a package publisher to modify and/or gain access to arbitrary files on a user\'s system when the package is installed. This behavior is still possible through install scripts. This vulnerability bypasses a user using the --ignore-scripts install option.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2019-12-13 01:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-16776                                                      ,/opt/app/node_modules/npx/node_modules/npm                                                                                                                                                       ,pkg:npm/npm@5.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-3918       ,           49 ,fail   ,nodejs ,critical ,json-schema          ,               ,0.2.3           ,(AFL-2.1 OR BSD-3-Clause) ,9.80 ,fixed in 0.4.0                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,json-schema is vulnerable to Improperly Controlled Modification of Object Prototype Attributes (\'Prototype Pollution\')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2021-11-13 09:15:06.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-3918                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/jsprim/node_modules/json-schema                                                         ,pkg:npm/json-schema@0.2.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7608       ,           49 ,fail   ,nodejs ,medium   ,yargs-parser         ,               ,13.0.0          ,ISC                       ,5.30 ,"fixed in 18.1.1, 15.0.1, 13.1.2,..." ,"Attack complexity: low, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"yargs-parser could be tricked into adding or modifying properties of Object.prototype using a \""__proto__\"" payload."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2020-03-16 20:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7608                                                       ,/opt/app/node_modules/typeorm/node_modules/yargs-parser                                                                                                                                          ,pkg:npm/yargs-parser@13.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-20149      ,           49 ,fail   ,nodejs ,high     ,kind-of              ,               ,6.0.2           ,MIT                       ,7.50 ,fixed in 6.0.3                        ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,"ctorName in index.js in kind-of v6.0.2 allows external user input to overwrite certain internal attributes via a conflicting name, as demonstrated by \'constructor\': {\'name\':\'Symbol\'}. Hence, a crafted payload can overwrite this builtin attribute to manipulate the type detection result."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2019-12-30 19:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-20149                                                      ,/opt/app/node_modules/randomatic/node_modules/kind-of                                                                                                                                            ,pkg:npm/kind-of@6.0.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10744      ,           49 ,fail   ,nodejs ,critical ,lodash               ,               ,4.17.11         ,MIT                       ,9.10 ,fixed in 4.17.12                      ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of lodash lower than 4.17.12 are vulnerable to Prototype Pollution. The function defaultsDeep could be tricked into adding or modifying properties of Object.prototype using a constructor payload.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2019-07-26 00:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10744                                                      ,/opt/app/node_modules/lodash                                                                                                                                                                     ,pkg:npm/lodash@4.17.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23337      ,           49 ,fail   ,nodejs ,high     ,lodash               ,               ,4.17.11         ,MIT                       ,7.20 ,fixed in 4.17.21                      ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,Lodash versions prior to 4.17.21 are vulnerable to Command Injection via the template function.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-02-15 13:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23337                                                      ,/opt/app/node_modules/lodash                                                                                                                                                                     ,pkg:npm/lodash@4.17.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8203       ,           49 ,fail   ,nodejs ,high     ,lodash               ,               ,4.17.11         ,MIT                       ,7.40 ,fixed in 4.17.20                      ,"Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"                             ,                   ,Prototype pollution attack when using _.zipObjectDeep in lodash before 4.17.20.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2020-07-15 17:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8203                                                       ,/opt/app/node_modules/lodash                                                                                                                                                                     ,pkg:npm/lodash@4.17.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-28500      ,           49 ,fail   ,nodejs ,medium   ,lodash               ,               ,4.17.11         ,MIT                       ,5.30 ,fixed in 4.17.21                      ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,"Lodash versions prior to 4.17.21 are vulnerable to Regular Expression Denial of Service (ReDoS) via the toNumber, trim and trimEnd functions."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-02-15 11:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-28500                                                      ,/opt/app/node_modules/lodash                                                                                                                                                                     ,pkg:npm/lodash@4.17.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10795      ,           49 ,fail   ,nodejs ,medium   ,undefsafe            ,               ,2.0.2           ,MIT                       ,6.30 ,fixed in 2.0.3                        ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,undefsafe before 2.0.3 is vulnerable to Prototype Pollution. The \'a\' function could be tricked into adding or modifying properties of Object.prototype using a __proto__ payload.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2020-02-18 16:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10795                                                      ,/opt/app/node_modules/undefsafe                                                                                                                                                                  ,pkg:npm/undefsafe@2.0.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-28469      ,           49 ,fail   ,nodejs ,high     ,glob-parent          ,               ,2.0.0           ,ISC                       ,7.50 ,fixed in 5.1.2                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,This affects the package glob-parent before 5.1.2. The enclosure regex used to check for strings ending in enclosure containing path separator.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-06-03 16:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-28469                                                      ,/opt/app/node_modules/glob-base/node_modules/glob-parent                                                                                                                                         ,pkg:npm/glob-parent@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10746      ,           49 ,fail   ,nodejs ,critical ,mixin-deep           ,               ,1.3.1           ,MIT                       ,9.80 ,fixed in 1.3.2                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,mixin-deep is vulnerable to Prototype Pollution in versions before 1.3.2 and version 2.0.0. The function mixin-deep could be tricked into adding or modifying properties of Object.prototype using a constructor payload.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,      ,              ,2019-08-23 17:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10746                                                      ,/opt/app/node_modules/mixin-deep                                                                                                                                                                 ,pkg:npm/mixin-deep@1.3.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23343      ,           49 ,fail   ,nodejs ,high     ,path-parse           ,               ,1.0.6           ,MIT                       ,7.50 ,fixed in 1.0.7                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"All versions of package path-parse are vulnerable to Regular Expression Denial of Service (ReDoS) via splitDeviceRe, splitTailRe, and splitPathRe regular expressions. ReDoS exhibits polynomial worst-case time complexity."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   ,      ,              ,2021-05-04 09:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23343                                                      ,/opt/app/node_modules/path-parse                                                                                                                                                                 ,pkg:npm/path-parse@1.0.6
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-29060      ,           49 ,fail   ,nodejs ,medium   ,color-string         ,               ,1.5.3           ,MIT                       ,5.30 ,fixed in 1.5.5                        ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,A Regular Expression Denial of Service (ReDOS) vulnerability was discovered in Color-String version 1.5.5 and below which occurs when the application is provided and checks a crafted invalid HWB string.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2021-06-21 16:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-29060                                                      ,/opt/app/node_modules/color-string                                                                                                                                                               ,pkg:npm/color-string@1.5.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-1109       ,           49 ,fail   ,nodejs ,medium   ,braces               ,               ,1.8.5           ,MIT                       ,5.30 ,fixed in 2.3.1                        ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,A vulnerability was found in Braces versions prior to 2.3.1. Affected versions of this package are vulnerable to Regular Expression Denial of Service (ReDoS) attacks.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2021-03-30 02:15:14.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-1109                                                       ,/opt/app/node_modules/gulp-load-plugins/node_modules/braces                                                                                                                                      ,pkg:npm/braces@1.8.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-g95f-p29q-9xw4 ,           49 ,fail   ,nodejs ,low      ,braces               ,               ,1.8.5           ,MIT                       ,3.70 ,fixed in 2.3.1                        ,"Attack vector: network, DoS - Low, Has fix"                                                                   ,                   ,Versions of `braces` prior to 2.3.1 are vulnerable to Regular Expression Denial of Service (ReDoS). Untrusted input may cause catastrophic backtracking while matching regular expressions. This can cause the application to be unresponsive leading to Denial of Service.   ## Recommendation  Upgrade to version 2.3.1 or higher.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2019-06-06 15:30:30.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-g95f-p29q-9xw4                                                    ,/opt/app/node_modules/gulp-load-plugins/node_modules/braces                                                                                                                                      ,pkg:npm/braces@1.8.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-4068       ,           49 ,fail   ,nodejs ,high     ,braces               ,               ,1.8.5           ,MIT                       ,7.50 ,fixed in 3.0.3                        ,"Has fix, High severity"                                                                                       ,                   ,"The NPM package `braces`, versions prior to 3.0.3, fails to limit the number of characters it can handle, which could lead to Memory Exhaustion. In `lib/parse.js,` if a malicious user sends \""imbalanced braces\"" as input, the parsing will enter a loop, which will cause the program to start allocating heap memory without freeing it at any moment of the loop. Eventually, the JavaScript heap limit is reached, and the program will crash."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,      ,              ,2024-05-14 15:42:48.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-4068                                                       ,/opt/app/node_modules/gulp-load-plugins/node_modules/braces                                                                                                                                      ,pkg:npm/braces@1.8.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-24999      ,           49 ,fail   ,nodejs ,high     ,qs                   ,               ,6.5.2           ,BSD-3-Clause              ,7.50 ,"fixed in 6.10.3, 6.9.7, 6.8.3,..."   ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,"qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \""deps: qs@6.9.7\"" in its release description, is not vulnerable)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2022-11-26 22:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-24999                                                      ,/opt/app/node_modules/request/node_modules/qs                                                                                                                                                    ,pkg:npm/qs@6.5.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10747      ,           49 ,fail   ,nodejs ,critical ,set-value            ,               ,0.4.3           ,MIT                       ,9.80 ,"fixed in 3.0.1, 2.0.1"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"set-value is vulnerable to Prototype Pollution in versions lower than 3.0.1. The function mixin-deep could be tricked into adding or modifying properties of Object.prototype using any of the constructor, prototype and _proto_ payloads."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,      ,              ,2019-08-23 17:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10747                                                      ,/opt/app/node_modules/union-value/node_modules/set-value                                                                                                                                         ,pkg:npm/set-value@0.4.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23440      ,           49 ,fail   ,nodejs ,critical ,set-value            ,               ,0.4.3           ,MIT                       ,9.80 ,"fixed in 4.0.1, 2.0.1"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,"This affects the package set-value before <2.0.1, >=3.0.0 <4.0.1. A type confusion vulnerability can lead to a bypass of CVE-2019-10747 when the user-provided keys used in the path parameter are arrays."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2021-09-12 13:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23440                                                      ,/opt/app/node_modules/union-value/node_modules/set-value                                                                                                                                         ,pkg:npm/set-value@0.4.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-8w57-jfpm-945m ,           49 ,fail   ,nodejs ,high     ,http-proxy-agent     ,               ,2.0.0           ,MIT                       ,7.00 ,fixed in 2.1.0                        ,"Has fix, High severity"                                                                                       ,                   ,Versions of `http-proxy-agent` before 2.1.0 are vulnerable to denial of service and uninitialized memory leak when unsanitized options are passed to `Buffer`. An attacker may leverage these unsanitized options to consume system resources.   ## Recommendation  Update to version 2.1.0 or later.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2019-06-11 16:16:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-8w57-jfpm-945m                                                    ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent                                                                      ,pkg:npm/http-proxy-agent@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10196      ,           49 ,fail   ,nodejs ,critical ,http-proxy-agent     ,               ,2.0.0           ,MIT                       ,9.80 ,fixed in 2.1.0                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"A flaw was found in http-proxy-agent, prior to version 2.1.0. It was discovered http-proxy-agent passes an auth option to the Buffer constructor without proper sanitization. This could result in a Denial of Service through the usage of all available CPU resources and data exposure through an uninitialized memory leak in setups where an attacker could submit typed input to the auth parameter."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2021-03-19 20:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10196                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-proxy-agent                                                                      ,pkg:npm/http-proxy-agent@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7788       ,           49 ,fail   ,nodejs ,critical ,ini                  ,               ,1.3.5           ,ISC                       ,9.80 ,fixed in 1.3.6                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"This affects the package ini before 1.3.6. If an attacker submits a malicious INI file to an application that parses it with ini.parse, they will pollute the prototype on the application. This can be exploited further depending on the context."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2020-12-11 11:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7788                                                       ,/opt/app/node_modules/npx/node_modules/ini                                                                                                                                                       ,pkg:npm/ini@1.3.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-21270      ,           49 ,fail   ,nodejs ,medium   ,stringstream         ,               ,0.0.5           ,MIT                       ,6.50 ,fixed in 0.0.6                        ,"Attack vector: network, DoS - High, Exploit exists - POC, Has fix, Medium severity"                           ,                   ,Versions less than 0.0.6 of the Node.js stringstream module are vulnerable to an out-of-bounds read because of allocation of uninitialized buffers when a number is passed in the input stream (when using Node.js 4.x).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2020-12-03 21:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-21270                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/stringstream                                                                                                        ,pkg:npm/stringstream@0.0.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23362      ,           49 ,fail   ,nodejs ,medium   ,hosted-git-info      ,               ,2.5.0           ,ISC                       ,5.30 ,"fixed in 3.0.8, 2.8.9"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,The package hosted-git-info before 3.0.8 are vulnerable to Regular Expression Denial of Service (ReDoS) via regular expression shortcutMatch in the fromUrl function in index.js. The affected regular expression exhibits polynomial worst-case time complexity.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2021-03-23 17:15:14.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23362                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/hosted-git-info                                                                                                                          ,pkg:npm/hosted-git-info@2.5.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25881      ,           49 ,fail   ,nodejs ,high     ,http-cache-semantics ,               ,3.7.3           ,BSD-2-Clause              ,7.50 ,fixed in 4.1.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"This affects versions of the package http-cache-semantics before 4.1.1. The issue can be exploited via malicious request header values sent to a server, when that server reads the cache policy from the request using this library."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2023-01-31 06:30:26.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25881                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/pacote/node_modules/make-fetch-happen/node_modules/http-cache-semantics                                                                  ,pkg:npm/http-cache-semantics@3.7.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-7651       ,           49 ,fail   ,nodejs ,medium   ,ssri                 ,               ,4.1.6           ,ISC                       ,5.90 ,fixed in 5.2.2                        ,"Attack vector: network, DoS - High, Has fix, Medium severity"                                                 ,                   ,index.js in the ssri module before 5.2.2 for Node.js is prone to a regular expression denial of service vulnerability in strict mode functionality via a long base64 hash string.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2018-03-04 01:29:00.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-7651                                                       ,/opt/app/node_modules/ssri                                                                                                                                                                       ,pkg:npm/ssri@4.1.6
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-28469      ,           49 ,fail   ,nodejs ,high     ,glob-parent          ,               ,3.1.0           ,ISC                       ,7.50 ,fixed in 5.1.2                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,This affects the package glob-parent before 5.1.2. The enclosure regex used to check for strings ending in enclosure containing path separator.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-06-03 16:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-28469                                                      ,/opt/app/node_modules/glob-parent                                                                                                                                                                ,pkg:npm/glob-parent@3.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-pc5p-h8pf-mvwp ,           49 ,fail   ,nodejs ,medium   ,https-proxy-agent    ,               ,2.2.1           ,MIT                       ,6.10 ,fixed in 2.2.3                        ,"Has fix, Medium severity"                                                                                     ,                   ,"Versions of `https-proxy-agent` prior to 2.2.3 are vulnerable to Machine-In-The-Middle. The package fails to enforce TLS on the socket if the proxy server responds the to the request with a HTTP status different than 200. This allows an attacker with access to the proxy server to intercept unencrypted communications, which may include sensitive information such as credentials.   ## Recommendation  Upgrade to version 3.0.0 or 2.2.3."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2020-04-16 03:14:56.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-pc5p-h8pf-mvwp                                                    ,/opt/app/node_modules/https-proxy-agent                                                                                                                                                          ,pkg:npm/https-proxy-agent@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0212    ,           49 ,fail   ,nodejs ,high     ,restify              ,               ,10.0.0          ,MIT                       ,7.50 ,open                                  ,High severity                                                                                                  ,                   ,resify packages from all versions are vulnerable for a Path Traversal vulnerability. Path normalization is occurring after request routing occurred which can lead to a Path Traversal vulnerability using ../ .                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2023-02-06 15:44:28.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/restify/node-restify/issues/1910                                                  ,/opt/app/node_modules/restify                                                                                                                                                                    ,pkg:npm/restify@10.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0213    ,           49 ,fail   ,nodejs ,high     ,restify              ,               ,10.0.0          ,MIT                       ,7.50 ,open                                  ,"DoS - High, High severity"                                                                                    ,                   ,restify packages from all versions are vulnerable for Denial of Service attack. \'fs.stat\' function \'file\' attribute is not checked for nullbyte allowing attacker to send a request containing %00 and crash the server : localhost:8080/%00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2023-02-06 15:59:53.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/restify/node-restify/issues/1864                                                  ,/opt/app/node_modules/restify                                                                                                                                                                    ,pkg:npm/restify@10.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-0842       ,           49 ,fail   ,nodejs ,medium   ,xml2js               ,               ,0.4.19          ,MIT                       ,5.30 ,fixed in 0.5.0                        ,"Attack complexity: low, Attack vector: network, Has fix, Medium severity"                                     ,                   ,"xml2js version 0.4.23 allows an external attacker to edit or add new properties to an object. This is possible because the application does not properly validate incoming JSON keys, thus allowing the __proto__ property to be edited."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ,      ,              ,2023-04-05 20:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-0842                                                       ,/opt/app/node_modules/typeorm/node_modules/xml2js                                                                                                                                                ,pkg:npm/xml2js@0.4.19
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23566      ,           49 ,fail   ,nodejs ,medium   ,nanoid               ,               ,3.1.20          ,MIT                       ,5.50 ,fixed in 3.1.31                       ,"Attack complexity: low, Exploit exists - POC, Has fix, Medium severity"                                       ,                   ,The package nanoid from 3.0.0 and before 3.1.31 are vulnerable to Information Exposure via the valueOf() function which allows to reproduce the last id generated.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,      ,              ,2022-01-14 20:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23566                                                      ,/opt/app/node_modules/nanoid                                                                                                                                                                     ,pkg:npm/nanoid@3.1.20
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-55565      ,           49 ,fail   ,nodejs ,medium   ,nanoid               ,               ,3.1.20          ,MIT                       ,4.30 ,"fixed in 5.0.9, 3.3.8"               ,"Has fix, Medium severity"                                                                                     ,                   ,nanoid (aka Nano ID) before 5.0.9 mishandles non-integer values. 3.3.8 is also a fixed version.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2024-12-09 02:15:19.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-55565                                                      ,/opt/app/node_modules/nanoid                                                                                                                                                                     ,pkg:npm/nanoid@3.1.20
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8244       ,           49 ,fail   ,nodejs ,medium   ,bl                   ,               ,1.2.2           ,MIT                       ,6.50 ,"fixed in 4.0.3, 3.0.1, 2.2.1,..."    ,"Attack complexity: low, Attack vector: network, DoS - Low, Exploit exists - POC, Has fix, Medium severity"    ,                   ,"A buffer over-read vulnerability exists in bl <4.0.3, <3.0.1, <2.2.1, and <1.2.3 which could allow an attacker to supply user input (even typed) that if it ends up in consume() argument and can become negative, the BufferList state can be corrupted, tricking it into exposing uninitialized memory via regular .slice() calls."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2020-08-30 15:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8244                                                       ,/opt/app/node_modules/bl                                                                                                                                                                         ,pkg:npm/bl@1.2.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-43138      ,           49 ,fail   ,nodejs ,high     ,async                ,               ,2.6.2           ,MIT                       ,7.80 ,"fixed in 3.2.2, 2.6.4"               ,"Attack complexity: low, DoS - High, Has fix, High severity"                                                   ,                   ,"In Async before 2.6.4 and 3.x before 3.2.2, a malicious user can obtain privileges via the mapValues() method, aka lib/internal/iterator.js createObjectIterator prototype pollution."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2022-04-06 17:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-43138                                                      ,/opt/app/node_modules/async                                                                                                                                                                      ,pkg:npm/async@2.6.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0230    ,           49 ,fail   ,nodejs ,high     ,mocha                ,               ,8.4.0           ,MIT                       ,7.50 ,fixed in 10.1.0                       ,"Has fix, High severity"                                                                                       ,                   ,org.webjars.npm_mocha packages from all versions are vulnerable to Regular Expression Denial of Service (ReDoS). clean() function is vulnerable to ReDoS attack due to the overlapped sub-patterns.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2022-07-07 11:33:32.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/mochajs/mocha/pull/4770                                                           ,/opt/app/node_modules/gulp-mocha/node_modules/mocha                                                                                                                                              ,pkg:npm/mocha@8.4.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0335    ,           49 ,fail   ,nodejs ,medium   ,mocha                ,               ,8.4.0           ,MIT                       ,5.30 ,open                                  ,Medium severity                                                                                                ,                   ,mocha packages from all versions are vulnerable to Regular Expression Denial of Service (ReDoS). clean() function in utils.js is vulnerable to ReDoS with the regex: /^function(?:\\s*|\\s+[^(]*)\\([^)]*\\)\\s*\\{((?:.|\n)*?)\\s*\\}$|^\\([^)]*\\                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2022-10-02 18:01:01.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/mochajs/mocha/commit/61b4b9209c2c64b32c8d48b1761c3b9384d411ea                     ,/opt/app/node_modules/gulp-mocha/node_modules/mocha                                                                                                                                              ,pkg:npm/mocha@8.4.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-7wwv-vh3v-89cq ,           49 ,fail   ,nodejs ,medium   ,highlight.js         ,               ,9.15.6          ,BSD-3-Clause              ,4.00 ,fixed in 10.4.1                       ,"Has fix, Medium severity"                                                                                     ,                   ,"### Impact: Potential ReDOS vulnerabilities (exponential and polynomial RegEx backtracking)  [oswasp](https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS):   > The Regular expression Denial of Service (ReDoS) is a Denial of Service attack, that exploits the fact that most Regular Expression implementations may reach extreme situations that cause them to work very slowly (exponentially related to input size). An attacker can then cause a program using a Regular Expression to enter these extreme situations and then hang for a very long time.  If are you are using Highlight.js to highlight user-provided data you are possibly vulnerable.  On the client-side (in a browser or Electron environment) risks could include lengthy freezes or crashes... On the server-side infinite freezes could occur... effectively preventing users from accessing your app or service (ie, Denial of Service).  This is an issue with grammars shipped with the parser (and potentially 3rd party grammars also), not the parser itself. If you are using Highlight.js with any of the following grammars you are vulnerable.  If you are using `highlightAuto` to detect the language (and have any of these grammars registered) you are vulnerable. Exponential grammars (C, Perl, JavaScript) are auto-registered when using the common grammar subset/library `require(\'highlight.js/lib/common\')` as "     ,      ,              ,2020-12-04 16:47:20.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-7wwv-vh3v-89cq                                                    ,/opt/app/node_modules/highlight.js                                                                                                                                                               ,pkg:npm/highlight.js@9.15.6
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-26237      ,           49 ,fail   ,nodejs ,high     ,highlight.js         ,               ,9.15.6          ,BSD-3-Clause              ,8.70 ,"fixed in 10.1.2, 9.18.2"             ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Highlight.js is a syntax highlighter written in JavaScript. Highlight.js versions before 9.18.2 and 10.1.2 are vulnerable to Prototype Pollution. A malicious HTML code block can be crafted that will result in prototype pollution of the base object\'s prototype during highlighting. If you allow users to insert custom HTML code blocks into your page/app via parsing Markdown code blocks (or similar) and do not filter the language names the user can provide you may be vulnerable. The pollution should just be harmless data but this can cause problems for applications not expecting these properties to exist and can result in strange behavior or application crashes, i.e. a potential DOS vector. If your website or application does not render user provided data it should be unaffected. Versions 9.18.2 and 10.1.2 and newer include fixes for this vulnerability. If you are using version 7 or 8 you are encouraged to upgrade to a newer release."                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,      ,              ,2020-11-24 23:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-26237                                                      ,/opt/app/node_modules/highlight.js                                                                                                                                                               ,pkg:npm/highlight.js@9.15.6
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-27290      ,           49 ,fail   ,nodejs ,high     ,ssri                 ,               ,5.3.0           ,ISC                       ,7.50 ,"fixed in 8.0.1, 6.0.2"               ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"ssri 5.2.2-8.0.0, fixed in 8.0.1, processes SRIs using a regular expression which is vulnerable to a denial of service. Malicious SRIs could take an extremely long time to process, leading to denial of service. This issue only affects consumers using the strict option."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2021-03-12 22:15:14.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-27290                                                      ,/opt/app/node_modules/make-fetch-happen/node_modules/ssri                                                                                                                                        ,pkg:npm/ssri@5.3.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-24999      ,           49 ,fail   ,nodejs ,high     ,qs                   ,               ,6.4.0           ,BSD-3-Clause              ,7.50 ,"fixed in 6.10.3, 6.9.7, 6.8.3,..."   ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,"qs before 6.10.3, as used in Express before 4.17.3 and other products, allows attackers to cause a Node process hang for an Express application because an __ proto__ key can be used. In many typical Express use cases, an unauthenticated remote attacker can place the attack payload in the query string of the URL that is used to visit the application, such as a[__proto__]=b&a[__proto__]&a[length]=100000000. The fix was backported to qs 6.9.7, 6.8.3, 6.7.3, 6.6.1, 6.5.3, 6.4.1, 6.3.3, and 6.2.4 (and therefore Express 4.17.3, which has \""deps: qs@6.9.7\"" in its release description, is not vulnerable)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2022-11-26 22:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-24999                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/qs                                                                                                                  ,pkg:npm/qs@6.4.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-31129      ,           49 ,fail   ,nodejs ,high     ,moment               ,               ,2.24.0          ,MIT                       ,7.50 ,fixed in 2.29.4                       ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,"moment is a JavaScript date library for parsing, validating, manipulating, and formatting dates. Affected versions of moment were found to use an inefficient parsing algorithm. Specifically using string-to-date parsing in moment (more specifically rfc2822 parsing, which is tried by default) has quadratic (N^2) complexity on specific inputs. Users may notice a noticeable slowdown is observed with inputs above 10k characters. Users who pass user-provided strings without sanity length checks to moment constructor are vulnerable to (Re)DoS attacks. The problem is patched in 2.29.4, the patch can be applied to all affected versions with minimal tweaking. Users are advised to upgrade. Users unable to upgrade should consider limiting date lengths accepted from user input."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,      ,              ,2022-07-06 18:15:19.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-31129                                                      ,/opt/app/node_modules/moment                                                                                                                                                                     ,pkg:npm/moment@2.24.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-24785      ,           49 ,fail   ,nodejs ,high     ,moment               ,               ,2.24.0          ,MIT                       ,7.50 ,fixed in 2.29.2                       ,"Attack complexity: low, Attack vector: network, Has fix, High severity"                                       ,                   ,"Moment.js is a JavaScript date library for parsing, validating, manipulating, and formatting dates. A path traversal vulnerability impacts npm (server) users of Moment.js between versions 1.0.1 and 2.29.1, especially if a user-provided locale string is directly used to switch moment locale. This problem is patched in 2.29.2, and the patch can be applied to all affected versions. As a workaround, sanitize the user-provided locale name before passing it to Moment.js."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2022-04-04 17:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-24785                                                      ,/opt/app/node_modules/moment                                                                                                                                                                     ,pkg:npm/moment@2.24.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25881      ,           49 ,fail   ,nodejs ,high     ,http-cache-semantics ,               ,3.8.1           ,BSD-2-Clause              ,7.50 ,fixed in 4.1.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"This affects versions of the package http-cache-semantics before 4.1.1. The issue can be exploited via malicious request header values sent to a server, when that server reads the cache policy from the request using this library."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2023-01-31 06:30:26.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25881                                                      ,/opt/app/node_modules/http-cache-semantics                                                                                                                                                       ,pkg:npm/http-cache-semantics@3.8.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7677       ,           49 ,fail   ,nodejs ,critical ,thenify              ,               ,3.3.0           ,MIT                       ,9.80 ,fixed in 3.3.1                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"This affects the package thenify before 3.3.1. The name argument provided to the package can be controlled by users without any sanitization, and this is provided to the eval function without any sanitization."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2022-07-25 14:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7677                                                       ,/opt/app/node_modules/thenify                                                                                                                                                                    ,pkg:npm/thenify@3.3.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-15366      ,           49 ,fail   ,nodejs ,medium   ,ajv                  ,               ,4.11.8          ,MIT                       ,5.60 ,fixed in 6.12.3                       ,"Attack vector: network, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"An issue was discovered in ajv.validate() in Ajv (aka Another JSON Schema Validator) 6.12.2. A carefully crafted JSON schema could be provided that allows execution of other code by prototype pollution. (While untrusted schemas are recommended against, the worst case of an untrusted schema should be a denial of service, not execution of code.)"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2020-07-15 20:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-15366                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/har-validator/node_modules/ajv                                                                                      ,pkg:npm/ajv@4.11.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-8158       ,           49 ,fail   ,nodejs ,critical ,typeorm              ,               ,0.2.16          ,MIT                       ,9.80 ,fixed in 0.2.25                       ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,Prototype pollution vulnerability in the TypeORM package < 0.2.25 may allow attackers to add or modify Object properties leading to further denial of service or SQL injection attacks.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2020-09-18 21:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-8158                                                       ,/opt/app/node_modules/typeorm                                                                                                                                                                    ,pkg:npm/typeorm@0.2.16
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-4068       ,           49 ,fail   ,nodejs ,high     ,braces               ,               ,2.3.2           ,MIT                       ,7.50 ,fixed in 3.0.3                        ,"Has fix, High severity"                                                                                       ,                   ,"The NPM package `braces`, versions prior to 3.0.3, fails to limit the number of characters it can handle, which could lead to Memory Exhaustion. In `lib/parse.js,` if a malicious user sends \""imbalanced braces\"" as input, the parsing will enter a loop, which will cause the program to start allocating heap memory without freeing it at any moment of the loop. Eventually, the JavaScript heap limit is reached, and the program will crash."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,      ,              ,2024-05-14 15:42:48.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-4068                                                       ,/opt/app/node_modules/braces                                                                                                                                                                     ,pkg:npm/braces@2.3.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-44906      ,           49 ,fail   ,nodejs ,critical ,minimist             ,               ,1.2.0           ,MIT                       ,9.80 ,fixed in 1.2.6                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2022-03-17 16:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-44906                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/minimist    ,pkg:npm/minimist@1.2.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7598       ,           49 ,fail   ,nodejs ,medium   ,minimist             ,               ,1.2.0           ,MIT                       ,5.60 ,fixed in 1.2.2                        ,"Attack vector: network, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \""constructor\"" or \""__proto__\"" payload."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2020-03-11 23:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7598                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/minimist    ,pkg:npm/minimist@1.2.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-21538      ,           49 ,fail   ,nodejs ,high     ,cross-spawn          ,               ,6.0.5           ,MIT                       ,7.50 ,"fixed in 7.0.5, 6.0.6"               ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package cross-spawn before 6.0.6, from 7.0.0 and before 7.0.5 are vulnerable to Regular Expression Denial of Service (ReDoS) due to improper input sanitization. An attacker can increase the CPU usage and crash the program by crafting a very large and well crafted string."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2024-11-08 06:30:47.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-21538                                                      ,/opt/app/node_modules/typeorm/node_modules/cross-spawn                                                                                                                                           ,pkg:npm/cross-spawn@6.0.5
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-15366      ,           49 ,fail   ,nodejs ,medium   ,ajv                  ,               ,6.10.0          ,MIT                       ,5.60 ,fixed in 6.12.3                       ,"Attack vector: network, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"An issue was discovered in ajv.validate() in Ajv (aka Another JSON Schema Validator) 6.12.2. A carefully crafted JSON schema could be provided that allows execution of other code by prototype pollution. (While untrusted schemas are recommended against, the worst case of an untrusted schema should be a denial of service, not execution of code.)"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2020-07-15 20:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-15366                                                      ,/opt/app/node_modules/ajv                                                                                                                                                                        ,pkg:npm/ajv@6.10.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-4067       ,           49 ,fail   ,nodejs ,medium   ,micromatch           ,               ,3.1.10          ,MIT                       ,5.30 ,fixed in 4.0.8                        ,"Has fix, Medium severity"                                                                                     ,                   ,"The NPM package `micromatch` prior to 4.0.8 is vulnerable to Regular Expression Denial of Service (ReDoS). The vulnerability occurs in `micromatch.braces()` in `index.js` because the pattern `.*` will greedily match anything. By passing a malicious payload, the pattern matching will keep backtracking to the input while it doesn\'t find the closing bracket. As the input size increases, the consumption time will also increase until it causes the application to hang or slow down. There was a merged fix but further testing shows the issue persists. This issue should be mitigated by using a safe pattern that won\'t start backtracking the regular expression due to greedy matching. This issue was fixed in version 4.0.8."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2024-05-14 15:42:47.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-4067                                                       ,/opt/app/node_modules/micromatch                                                                                                                                                                 ,pkg:npm/micromatch@3.1.10
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-21670      ,           49 ,fail   ,nodejs ,medium   ,markdown-it          ,               ,8.4.2           ,MIT                       ,5.30 ,fixed in 12.3.2                       ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,"markdown-it is a Markdown parser. Prior to version 1.3.2, special patterns with length greater than 50 thousand characterss could slow down the parser significantly. Users should upgrade to version 12.3.2 to receive a patch. There are no known workarounds aside from upgrading."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2022-01-10 21:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-21670                                                      ,/opt/app/node_modules/markdown-it                                                                                                                                                                ,pkg:npm/markdown-it@8.4.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-45813      ,           49 ,fail   ,nodejs ,high     ,find-my-way          ,               ,7.7.0           ,MIT                       ,7.50 ,"fixed in 9.0.1, 8.2.2"               ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"find-my-way is a fast, open source HTTP router, internally using a Radix Tree (aka compact Prefix Tree), supports route params, wildcards, and it\'s framework independent. A bad regular expression is generated any time one has two parameters within a single segment, when adding a `-` at the end, like `/:a-:b-`. This may cause a denial of service in some instances. Users are advised to update to find-my-way v8.2.2 or v9.0.1. or subsequent versions. There are no known workarounds for this issue."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2024-09-18 15:52:33.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-45813                                                      ,/opt/app/node_modules/find-my-way                                                                                                                                                                ,pkg:npm/find-my-way@7.7.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-3737       ,           49 ,fail   ,nodejs ,high     ,sshpk                ,               ,1.13.1          ,MIT                       ,7.50 ,                                      ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, High severity"              ,                   ,sshpk is vulnerable to ReDoS when parsing crafted invalid public keys.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2018-06-07 02:29:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-3737                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/sshpk                                                                                   ,pkg:npm/sshpk@1.13.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-44906      ,           49 ,fail   ,nodejs ,critical ,minimist             ,               ,0.0.8           ,MIT                       ,9.80 ,fixed in 1.2.6                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"Minimist <=1.2.5 is vulnerable to Prototype Pollution via file index.js, function setKey() (lines 69-95)."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2022-03-17 16:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-44906                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/mkdirp/node_modules/minimist                                                                                                             ,pkg:npm/minimist@0.0.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7598       ,           49 ,fail   ,nodejs ,medium   ,minimist             ,               ,0.0.8           ,MIT                       ,5.60 ,fixed in 1.2.2                        ,"Attack vector: network, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"minimist before 1.2.2 could be tricked into adding or modifying properties of Object.prototype using a \""constructor\"" or \""__proto__\"" payload."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,      ,              ,2020-03-11 23:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7598                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/mkdirp/node_modules/minimist                                                                                                             ,pkg:npm/minimist@0.0.8
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-3728       ,           49 ,fail   ,nodejs ,high     ,hoek                 ,               ,2.16.3          ,BSD-3-Clause              ,8.80 ,"fixed in 5.0.3, 4.2.0"               ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,"hoek node module before 4.2.0 and 5.0.x before 5.0.3 suffers from a Modification of Assumed-Immutable Data (MAID) vulnerability via \'merge\' and \'applyToDefaults\' functions, which allows a malicious user to modify the prototype of \""Object\"" via __proto__, causing the addition or modification of an existing property that will exist on all objects."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2018-03-30 19:29:00.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-3728                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/hoek                                                                                              ,pkg:npm/hoek@2.16.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-36604      ,           49 ,fail   ,nodejs ,high     ,hoek                 ,               ,2.16.3          ,BSD-3-Clause              ,8.10 ,"fixed in 9.0.3, 8.5.1"               ,"Attack vector: network, DoS - High, Has fix, High severity"                                                   ,                   ,hoek before 8.5.1 and 9.x before 9.0.3 allows prototype poisoning in the clone function.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2022-09-23 06:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-36604                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk/node_modules/hoek                                                                                              ,pkg:npm/hoek@2.16.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-28155      ,           49 ,fail   ,nodejs ,medium   ,request              ,               ,2.81.0          ,Apache-2.0                ,6.10 ,                                      ,"Attack complexity: low, Attack vector: network, Medium severity"                                              ,                   ,"The Request package through 2.88.1 for Node.js allows a bypass of SSRF mitigations via an attacker-controller server that does a cross-protocol redirect (HTTP to HTTPS, or HTTPS to HTTP). NOTE: This vulnerability only affects products that are no longer supported by the maintainer."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-03-16 15:15:11.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-28155                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request                                                                                                                                  ,pkg:npm/request@2.81.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-3750       ,           49 ,fail   ,nodejs ,critical ,deep-extend          ,               ,0.4.2           ,MIT                       ,9.80 ,fixed in 0.5.1                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,The utilities function in all versions <= 0.5.0 of the deep-extend node module can be tricked into modifying the prototype of Object when the attacker can control part of the structure passed to this function. This can let an attacker add or modify existing properties that will exist on all objects.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2018-07-03 21:29:00.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-3750                                                       ,/opt/app/node_modules/npx/node_modules/npm/node_modules/update-notifier/node_modules/latest-version/node_modules/package-json/node_modules/registry-url/node_modules/rc/node_modules/deep-extend ,pkg:npm/deep-extend@0.4.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0212    ,           49 ,fail   ,nodejs ,high     ,restify              ,               ,4.3.4           ,MIT                       ,7.50 ,open                                  ,High severity                                                                                                  ,                   ,resify packages from all versions are vulnerable for a Path Traversal vulnerability. Path normalization is occurring after request routing occurred which can lead to a Path Traversal vulnerability using ../ .                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2023-02-06 15:44:28.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/restify/node-restify/issues/1910                                                  ,/opt/app/node_modules/restify-health-router/node_modules/restify                                                                                                                                 ,pkg:npm/restify@4.3.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0213    ,           49 ,fail   ,nodejs ,high     ,restify              ,               ,4.3.4           ,MIT                       ,7.50 ,open                                  ,"DoS - High, High severity"                                                                                    ,                   ,restify packages from all versions are vulnerable for Denial of Service attack. \'fs.stat\' function \'file\' attribute is not checked for nullbyte allowing attacker to send a request containing %00 and crash the server : localhost:8080/%00                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2023-02-06 15:59:53.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/restify/node-restify/issues/1864                                                  ,/opt/app/node_modules/restify-health-router/node_modules/restify                                                                                                                                 ,pkg:npm/restify@4.3.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-26136      ,           49 ,fail   ,nodejs ,critical ,tough-cookie         ,               ,2.3.4           ,BSD-3-Clause              ,9.80 ,fixed in 4.1.3                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-07-01 05:15:16.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-26136                                                      ,/opt/app/node_modules/npm/node_modules/tough-cookie                                                                                                                                              ,pkg:npm/tough-cookie@2.3.4
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-10747      ,           49 ,fail   ,nodejs ,critical ,set-value            ,               ,2.0.0           ,MIT                       ,9.80 ,"fixed in 3.0.1, 2.0.1"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"set-value is vulnerable to Prototype Pollution in versions lower than 3.0.1. The function mixin-deep could be tricked into adding or modifying properties of Object.prototype using any of the constructor, prototype and _proto_ payloads."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,      ,              ,2019-08-23 17:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-10747                                                      ,/opt/app/node_modules/set-value                                                                                                                                                                  ,pkg:npm/set-value@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23440      ,           49 ,fail   ,nodejs ,critical ,set-value            ,               ,2.0.0           ,MIT                       ,9.80 ,"fixed in 4.0.1, 2.0.1"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,"This affects the package set-value before <2.0.1, >=3.0.0 <4.0.1. A type confusion vulnerability can lead to a bypass of CVE-2019-10747 when the user-provided keys used in the path parameter are arrays."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2021-09-12 13:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23440                                                      ,/opt/app/node_modules/set-value                                                                                                                                                                  ,pkg:npm/set-value@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-3807       ,           49 ,fail   ,nodejs ,high     ,ansi-regex           ,               ,4.1.0           ,MIT                       ,7.50 ,fixed in 4.1.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Exploit exists - POC, Has fix, High severity"     ,                   ,ansi-regex is vulnerable to Inefficient Regular Expression Complexity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2021-09-17 07:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-3807                                                       ,/opt/app/node_modules/typeorm/node_modules/ansi-regex                                                                                                                                            ,pkg:npm/ansi-regex@4.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-29167      ,           49 ,fail   ,nodejs ,high     ,hawk                 ,               ,3.1.3           ,BSD-3-Clause              ,7.50 ,fixed in 9.0.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Hawk is an HTTP authentication scheme providing mechanisms for making authenticated HTTP requests with partial cryptographic verification of the request and response, covering the HTTP method, request URI, host, and optionally the request payload. Hawk used a regular expression to parse `Host` HTTP header (`Hawk.utils.parseHost()`), which was subject to regular expression DoS attack - meaning each added character in the attacker\'s input increases the computation time exponentially. `parseHost()` was patched in `9.0.1` to use built-in `URL` class to parse hostname instead. `Hawk.authenticate()` accepts `options` argument. If that contains `host` and `port`, those would be used instead of a call to `utils.parseHost()`."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,      ,              ,2022-05-05 23:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-29167                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/hawk                                                                                                                ,pkg:npm/hawk@3.1.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-12905      ,           49 ,fail   ,nodejs ,high     ,tar-fs               ,               ,1.16.3          ,MIT                       ,7.50 ,"fixed in 3.0.7, 2.1.2, 1.16.4"       ,"Has fix, High severity, Recent vulnerability"                                                                 ,                   ,"An Improper Link Resolution Before File Access (\""Link Following\"") and Improper Limitation of a Pathname to a Restricted Directory (\""Path Traversal\""). This vulnerability occurs when extracting a maliciously crafted tar file, which can result in unauthorized file writes or overwrites outside the intended extraction directory. The issue is associated with index.js in the tar-fs package.  This issue affects tar-fs: from 0.0.0 before 1.16.4, from 2.0.0 before 2.1.2, from 3.0.0 before 3.0.8."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2025-03-27 17:15:53.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-12905                                                      ,/opt/app/node_modules/tar-fs                                                                                                                                                                     ,pkg:npm/tar-fs@1.16.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2025-48387      ,           49 ,fail   ,nodejs ,high     ,tar-fs               ,               ,1.16.3          ,MIT                       ,8.70 ,"fixed in 3.0.9, 2.1.3, 1.16.5"       ,"Has fix, High severity, Recent vulnerability"                                                                 ,                   ,"tar-fs provides filesystem bindings for tar-stream. Versions prior to 3.0.9, 2.1.3, and 1.16.5 have an issue where an extract can write outside the specified dir with a specific tarball. This has been patched in versions 3.0.9, 2.1.3, and 1.16.5. As a workaround, use the ignore option to ignore non files/directories."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2025-06-02 20:15:22.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2025-48387                                                      ,/opt/app/node_modules/tar-fs                                                                                                                                                                     ,pkg:npm/tar-fs@1.16.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,GHSA-4xcv-9jjx-gfj3 ,           49 ,fail   ,nodejs ,medium   ,mem                  ,               ,1.1.0           ,MIT                       ,5.10 ,fixed in 4.0.0                        ,"Has fix, Medium severity"                                                                                     ,                   ,Versions of `mem` prior to 4.0.0 are vulnerable to Denial of Service (DoS).  The package fails to remove old values from the cache even after a value passes its `maxAge` property. This may allow attackers to exhaust the system\'s memory if they are able to abuse the application logging.   ## Recommendation  Upgrade to version 4.0.0 or later.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2019-07-05 21:07:58.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/advisories/GHSA-4xcv-9jjx-gfj3                                                    ,/opt/app/node_modules/npx/node_modules/mem                                                                                                                                                       ,pkg:npm/mem@1.1.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7774       ,           49 ,fail   ,nodejs ,critical ,y18n                 ,               ,3.2.1           ,ISC                       ,9.80 ,"fixed in 5.0.5, 3.2.2"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,"The package y18n before 3.2.2, 4.0.1 and 5.0.5, is vulnerable to Prototype Pollution."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ,      ,              ,2020-11-17 13:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7774                                                       ,/opt/app/node_modules/y18n                                                                                                                                                                       ,pkg:npm/y18n@3.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-45296      ,           49 ,fail   ,nodejs ,high     ,path-to-regexp       ,               ,1.7.0           ,MIT                       ,7.50 ,"fixed in 8.0.0, 6.3.0, 3.3.0,..."    ,"Has fix, High severity"                                                                                       ,                   ,"path-to-regexp turns path strings into a regular expressions. In certain cases, path-to-regexp will output a regular expression that can be exploited to cause poor performance. Because JavaScript is single threaded and regex matching runs on the main thread, poor performance will block the event loop and lead to a DoS. The bad regular expression is generated any time you have two parameters within a single segment, separated by something that is not a period (.). For users of 0.1, upgrade to 0.1.10. All other users should upgrade to 8.0.0."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2024-09-09 19:15:13.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-45296                                                      ,/opt/app/node_modules/path-to-regexp                                                                                                                                                             ,pkg:npm/path-to-regexp@1.7.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0230    ,           49 ,fail   ,nodejs ,high     ,mocha                ,               ,5.2.0           ,MIT                       ,7.50 ,fixed in 10.1.0                       ,"Has fix, High severity"                                                                                       ,                   ,org.webjars.npm_mocha packages from all versions are vulnerable to Regular Expression Denial of Service (ReDoS). clean() function is vulnerable to ReDoS attack due to the overlapped sub-patterns.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2022-07-07 11:33:32.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/mochajs/mocha/pull/4770                                                           ,/opt/app/node_modules/mocha                                                                                                                                                                      ,pkg:npm/mocha@5.2.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2022-0335    ,           49 ,fail   ,nodejs ,medium   ,mocha                ,               ,5.2.0           ,MIT                       ,5.30 ,open                                  ,Medium severity                                                                                                ,                   ,mocha packages from all versions are vulnerable to Regular Expression Denial of Service (ReDoS). clean() function in utils.js is vulnerable to ReDoS with the regex: /^function(?:\\s*|\\s+[^(]*)\\([^)]*\\)\\s*\\{((?:.|\n)*?)\\s*\\}$|^\\([^)]*\\                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ,      ,              ,2022-10-02 18:01:01.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/mochajs/mocha/commit/61b4b9209c2c64b32c8d48b1761c3b9384d411ea                     ,/opt/app/node_modules/mocha                                                                                                                                                                      ,pkg:npm/mocha@5.2.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25883      ,           49 ,fail   ,nodejs ,high     ,semver               ,               ,5.3.0           ,ISC                       ,7.50 ,"fixed in 7.5.2, 6.3.1, 5.7.2"        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package semver before 7.5.2 are vulnerable to Regular Expression Denial of Service (ReDoS) via the function new Range, when untrusted user data is provided as a range.\r\r\r"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2023-06-21 05:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25883                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/semver                                                                                                                                   ,pkg:npm/semver@5.3.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2020-7608       ,           49 ,fail   ,nodejs ,medium   ,yargs-parser         ,               ,9.0.2           ,ISC                       ,5.30 ,"fixed in 18.1.1, 15.0.1, 13.1.2,..." ,"Attack complexity: low, DoS - Low, Has fix, Medium severity"                                                  ,                   ,"yargs-parser could be tricked into adding or modifying properties of Object.prototype using a \""__proto__\"" payload."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         ,      ,              ,2020-03-16 20:15:12.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2020-7608                                                       ,/opt/app/node_modules/yargs/node_modules/yargs-parser                                                                                                                                            ,pkg:npm/yargs-parser@9.0.2
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2019-17592      ,           49 ,fail   ,nodejs ,high     ,csv-parse            ,               ,1.3.3           ,BSD-3-Clause              ,7.50 ,fixed in 4.4.6                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,The csv-parse module before 4.4.6 for Node.js is vulnerable to Regular Expression Denial of Service. The __isInt() function contains a malformed regular expression that processes large crafted input very slowly. This is triggered when using the cast option.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2019-10-14 20:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2019-17592                                                      ,/opt/app/node_modules/restify-health-router/node_modules/csv-parse                                                                                                                               ,pkg:npm/csv-parse@1.3.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-26136      ,           49 ,fail   ,nodejs ,critical ,tough-cookie         ,               ,2.4.3           ,BSD-3-Clause              ,9.80 ,fixed in 4.1.3                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-07-01 05:15:16.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-26136                                                      ,/opt/app/node_modules/request/node_modules/tough-cookie                                                                                                                                          ,pkg:npm/tough-cookie@2.4.3
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25883      ,           49 ,fail   ,nodejs ,high     ,semver               ,               ,5.5.0           ,ISC                       ,7.50 ,"fixed in 7.5.2, 6.3.1, 5.7.2"        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package semver before 7.5.2 are vulnerable to Regular Expression Denial of Service (ReDoS) via the function new Range, when untrusted user data is provided as a range.\r\r\r"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2023-06-21 05:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25883                                                      ,/opt/app/node_modules/npx/node_modules/semver                                                                                                                                                    ,pkg:npm/semver@5.5.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2022-25883      ,           49 ,fail   ,nodejs ,high     ,semver               ,               ,4.3.6           ,ISC                       ,7.50 ,"fixed in 7.5.2, 6.3.1, 5.7.2"        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"Versions of the package semver before 7.5.2 are vulnerable to Regular Expression Denial of Service (ReDoS) via the function new Range, when untrusted user data is provided as a range.\r\r\r"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ,      ,              ,2023-06-21 05:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2022-25883                                                      ,/opt/app/node_modules/restify-health-router/node_modules/semver                                                                                                                                  ,pkg:npm/semver@4.3.6
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-4067       ,           49 ,fail   ,nodejs ,medium   ,micromatch           ,               ,2.3.11          ,MIT                       ,5.30 ,fixed in 4.0.8                        ,"Has fix, Medium severity"                                                                                     ,                   ,"The NPM package `micromatch` prior to 4.0.8 is vulnerable to Regular Expression Denial of Service (ReDoS). The vulnerability occurs in `micromatch.braces()` in `index.js` because the pattern `.*` will greedily match anything. By passing a malicious payload, the pattern matching will keep backtracking to the input while it doesn\'t find the closing bracket. As the input size increases, the consumption time will also increase until it causes the application to hang or slow down. There was a merged fix but further testing shows the issue persists. This issue should be mitigated by using a safe pattern that won\'t start backtracking the regular expression due to greedy matching. This issue was fixed in version 4.0.8."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ,      ,              ,2024-05-14 15:42:47.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-4067                                                       ,/opt/app/node_modules/gulp-load-plugins/node_modules/micromatch                                                                                                                                  ,pkg:npm/micromatch@2.3.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-26136      ,           49 ,fail   ,nodejs ,critical ,tough-cookie         ,               ,2.5.0           ,BSD-3-Clause              ,9.80 ,fixed in 4.1.3                        ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Has fix"                       ,                   ,Versions of the package tough-cookie before 4.1.3 are vulnerable to Prototype Pollution due to improper handling of Cookies when using CookieJar in rejectPublicSuffixes=false mode. This issue arises from the manner in which the objects are initialized.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ,      ,              ,2023-07-01 05:15:16.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-26136                                                      ,/opt/app/node_modules/tough-cookie                                                                                                                                                               ,pkg:npm/tough-cookie@2.5.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2023-43646      ,           49 ,fail   ,nodejs ,high     ,get-func-name        ,               ,2.0.0           ,MIT                       ,7.50 ,fixed in 2.0.1                        ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"get-func-name is a module to retrieve a function\'s name securely and consistently both in NodeJS and the browser. Versions prior to 2.0.1 are subject to a regular expression denial of service (redos) vulnerability which may lead to a denial of service when parsing malicious input. This vulnerability can be exploited when there is an imbalance in parentheses, which results in excessive backtracking and subsequently increases the CPU load and processing time significantly. This vulnerability can be triggered using the following input: \'\\t\'.repeat(54773) + \'\\t/function/i\'. This issue has been addressed in commit `f934b228b` which has been included in releases from 2.0.1. Users are advised to upgrade. There are no known workarounds for this vulnerability."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,      ,              ,2023-09-27 15:19:34.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2023-43646                                                      ,/opt/app/node_modules/get-func-name                                                                                                                                                              ,pkg:npm/get-func-name@2.0.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2025-5889       ,           49 ,fail   ,nodejs ,low      ,brace-expansion      ,               ,1.1.11          ,MIT                       ,3.10 ,"fixed in 4.0.1, 3.0.1, 2.0.2,..."    ,"Has fix, Recent vulnerability"                                                                                ,                   ,"A vulnerability was found in juliangruber brace-expansion up to 1.1.11/2.0.1/3.0.0/4.0.0. It has been rated as problematic. Affected by this issue is the function expand of the file index.js. The manipulation leads to inefficient regular expression complexity. The attack may be launched remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. Upgrading to version 1.1.12, 2.0.2, 3.0.1 and 4.0.1 is able to address this issue. The name of the patch is a5b98a4f30d7813266b221435e1eaaf25a1b0ac5. It is recommended to upgrade the affected component."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      ,      ,              ,2025-06-09 19:15:25.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2025-5889                                                       ,/opt/app/node_modules/npx/node_modules/brace-expansion                                                                                                                                           ,pkg:npm/brace-expansion@1.1.11
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,PRISMA-2021-0096    ,           49 ,fail   ,nodejs ,low      ,tar                  ,               ,2.2.1           ,ISC                       ,3.50 ,"fixed in 6.1.4, 4.4.16, 5.0.8"       ,Has fix                                                                                                        ,                   ,"tar package versions before 6.1.4, 4.4.16, and 5.0.8 are vulnerable to Regular Expression Denial of Service (ReDoS). When stripping the trailing slash from `files` arguments, we were using `f.replace(/\\/+$/, \'\')`, which can get exponentially slow when `f` contains many `/` characters. This is \""\""unlikely but theoretically possible\""\"" because it requires that the user is passing untrusted input into the `tar.extract()` or `tar.list()` array of entries to parse/extract, which would be quite unusual."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2021-08-30 08:44:48.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://github.com/npm/node-tar/commit/06cbde5935aa7643f578f874de84a7da2a74fe3a                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-20834      ,           49 ,fail   ,nodejs ,high     ,tar                  ,               ,2.2.1           ,ISC                       ,7.50 ,"fixed in 4.4.2, 2.2.2"               ,"Attack complexity: low, Attack vector: network, Exploit exists - POC, Has fix, High severity"                 ,                   ,"A vulnerability was found in node-tar before version 4.4.2 (excluding version 2.2.2). An Arbitrary File Overwrite issue exists when extracting a tarball containing a hardlink to a file that already exists on the system, in conjunction with a later plain file with the same name as the hardlink. This plain file content replaces the existing file content. A patch has been applied to node-tar v2.2.2"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ,      ,              ,2019-04-30 19:29:03.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-20834                                                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-37701      ,           49 ,fail   ,nodejs ,high     ,tar                  ,               ,2.2.1           ,ISC                       ,8.60 ,"fixed in 6.1.7, 5.0.8, 4.4.16"       ,"Attack complexity: low, DoS - High, Has fix, High severity, Remote execution"                                 ,                   ,"The npm package \""tar\"" (aka node-tar) before versions 4.4.16, 5.0.8, and 6.1.7 has an arbitrary file creation/overwrite and arbitrary code execution vulnerability. node-tar aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary stat calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory, where the symlink and directory names in the archive entry used backslashes as a path separator on posix systems. The cache checking logic used both `\\` and `/` characters as path separators, however `\\` is a valid filename character on posix systems. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass node-tar symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. Additionally, a similar confusion could arise on case-insensitive filesystems. If a tar archive contained a directory at `FOO`, followed by a symboli" ,      ,              ,2021-08-31 17:15:07.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-37701                                                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-32803      ,           49 ,fail   ,nodejs ,high     ,tar                  ,               ,2.2.1           ,ISC                       ,8.10 ,"fixed in 6.1.2, 5.0.7, 4.4.15,..."   ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"The npm package \""tar\"" (aka node-tar) before versions 6.1.2, 5.0.7, 4.4.15, and 3.2.3 has an arbitrary File Creation/Overwrite vulnerability via insufficient symlink protection. `node-tar` aims to guarantee that any file whose location would be modified by a symbolic link is not extracted. This is, in part, achieved by ensuring that extracted directories are not symlinks. Additionally, in order to prevent unnecessary `stat` calls to determine whether a given path is a directory, paths are cached when directories are created. This logic was insufficient when extracting tar files that contained both a directory and a symlink with the same name as the directory. This order of operations resulted in the directory being created and added to the `node-tar` directory cache. When a directory is present in the directory cache, subsequent calls to mkdir for that directory are skipped. However, this is also where `node-tar` checks for symlinks occur. By first creating a directory, and then replacing that directory with a symlink, it was thus possible to bypass `node-tar` symlink checks on directories, essentially allowing an untrusted tar file to symlink into an arbitrary location and subsequently extracting arbitrary files into that location, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.3, 4.4.15, 5.0.7 and 6.1.2."                              ,      ,              ,2021-08-03 19:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-32803                                                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-32804      ,           49 ,fail   ,nodejs ,high     ,tar                  ,               ,2.2.1           ,ISC                       ,8.10 ,"fixed in 6.1.1, 5.0.6, 4.4.14,..."   ,"Attack complexity: low, Attack vector: network, DoS - High, Has fix, High severity"                           ,                   ,"The npm package \""tar\"" (aka node-tar) before versions 6.1.1, 5.0.6, 4.4.14, and 3.3.2 has a arbitrary File Creation/Overwrite vulnerability due to insufficient absolute path sanitization. node-tar aims to prevent extraction of absolute file paths by turning absolute paths into relative paths when the `preservePaths` flag is not set to `true`. This is achieved by stripping the absolute path root from any absolute file paths contained in a tar file. For example `/home/<USER>/.bashrc` would turn into `home/user/.bashrc`. This logic was insufficient when file paths contained repeated path roots such as `////home/<USER>/.bashrc`. `node-tar` would only strip a single path root from such paths. When given an absolute file path with repeating path roots, the resulting path (e.g. `///home/<USER>/.bashrc`) would still resolve to an absolute path, thus allowing arbitrary file creation and overwrite. This issue was addressed in releases 3.2.2, 4.4.14, 5.0.6 and 6.1.1. Users may work around this vulnerability without upgrading by creating a custom `onentry` method which sanitizes the `entry.path` or a `filter` method which removes entries with absolute paths. See referenced GitHub Advisory for details. Be aware of CVE-2021-32803 which fixes a similar bug in later versions of tar."                                                                                                                           ,      ,              ,2021-08-03 19:15:08.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-32804                                                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-28863      ,           49 ,fail   ,nodejs ,medium   ,tar                  ,               ,2.2.1           ,ISC                       ,6.50 ,fixed in 6.2.1                        ,"Has fix, Medium severity"                                                                                     ,                   ,node-tar is a Tar for Node.js. node-tar prior to version 6.2.1 has no limit on the number of sub-folders created in the folder creation process. An attacker who generates a large number of sub-folders can consume memory on the system running node-tar and even crash the Node.js client within few seconds of running it using a path with too many sub-folders inside. Version 6.2.1 fixes this issue by preventing extraction in excessively deep sub-folders.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ,      ,              ,2024-03-21 23:15:10.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-28863                                                      ,/opt/app/node_modules/tar                                                                                                                                                                        ,pkg:npm/tar@2.2.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2017-18869      ,           49 ,fail   ,nodejs ,low      ,chownr               ,               ,1.0.1           ,ISC                       ,2.50 ,fixed in 1.1.0                        ,Has fix                                                                                                        ,                   ,A TOCTOU issue in the chownr package before 1.1.0 for Node.js 10.10 could allow a local attacker to trick it into descending into unintended directories via symlink attacks.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,      ,              ,2020-06-15 15:15:09.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2017-18869                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/chownr                                                                                                                                   ,pkg:npm/chownr@1.0.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2024-43799      ,           49 ,fail   ,nodejs ,medium   ,send                 ,               ,0.18.0          ,MIT                       ,4.70 ,fixed in 0.19.0                       ,"Attack vector: network, Has fix, Medium severity"                                                             ,                   ,Send is a library for streaming files from the file system as a http response. Send passes untrusted user input to SendStream.redirect() which executes untrusted code. This issue is patched in send 0.19.0.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,      ,              ,2024-09-10 15:15:17.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2024-43799                                                      ,/opt/app/node_modules/send                                                                                                                                                                       ,pkg:npm/send@0.18.0
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2018-16492      ,           49 ,fail   ,nodejs ,critical ,extend               ,               ,3.0.1           ,MIT                       ,9.80 ,"fixed in 3.0.2, 2.0.2"               ,"Attack complexity: low, Attack vector: network, Critical severity, DoS - High, Exploit exists - POC, Has fix" ,                   ,"A prototype pollution vulnerability was found in module extend <2.0.2, ~<3.0.2 that allows an attacker to inject arbitrary properties onto Object.prototype."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   ,      ,              ,2019-02-01 18:29:01.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2018-16492                                                      ,/opt/app/node_modules/npx/node_modules/npm/node_modules/request/node_modules/extend                                                                                                              ,pkg:npm/extend@3.0.1
277983268692.dkr.ecr.us-east-1.amazonaws.com ,post-security-manager ,1.1.368 ,277983268692.dkr.ecr.us-east-1.amazonaws.com/post-security-manager:1.1.368 ,alpine-3.22.0 ,ip-10-80-3-155.ec2.internal ,"{""created"":**********,""instruction"":""COPY file:3a3e1464004bbc305e73f1d66799e23929885d60426465ef0da59c3495400cb6 in . "",""sizeBytes"":8303,""id"":""\u003cmissing\u003e"",""emptyLayer"":false}" ,CVE-2021-23362      ,           49 ,fail   ,nodejs ,medium   ,hosted-git-info      ,               ,2.7.1           ,ISC                       ,5.30 ,"fixed in 3.0.8, 2.8.9"               ,"Attack complexity: low, Attack vector: network, DoS - Low, Has fix, Medium severity"                          ,                   ,The package hosted-git-info before 3.0.8 are vulnerable to Regular Expression Denial of Service (ReDoS) via regular expression shortcutMatch in the fromUrl function in index.js. The affected regular expression exhibits polynomial worst-case time complexity.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,      ,              ,2021-03-23 17:15:14.000 ,          ,sha256:7ee3753cf38c313b06b06e9deed1ee796c954af22b1f47d274c9251933f3bd53 ,https://nvd.nist.gov/vuln/detail/CVE-2021-23362                                                      ,/opt/app/node_modules/npm/node_modules/normalize-package-data/node_modules/hosted-git-info                                                                                                       ,pkg:npm/hosted-git-info@2.7.1
