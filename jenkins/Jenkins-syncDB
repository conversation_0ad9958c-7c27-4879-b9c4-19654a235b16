def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null


pipeline {
    environment {
        ENV = "${params.environmentToDeploy}"
        RUN_MODE = "${params.mode}"
    }
    // agent none -->> means it should not need any specific agent
    // agent any -->> it will try to run on any node available
    agent {
        node {
          label 'ubuntu-18'
        }
    }

    stages {
        stage("Sync DB") {
            steps {
                println "Build"
                script {
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                    def etd = env['environmentToDeploy']
                    awsDeployer = ''
                    switch (etd) {
                        case 'dev':
                            awsDeployer = 'dev-amrpwl-aws-deployer'
                            break
                        case 'int':
                            awsDeployer = 'nonprod-amrpwl-aws-deployer'
                            break
                        case 'uat':
                            awsDeployer = 'uat-amrpwl-aws-deployer'
                            break
                        case 'prod':
                            awsDeployer = 'prod-amrpwl-aws-deployer'
                            break
                    }
                    println 'deployer is ' + awsDeployer
                }
                // https://jenkins.io/doc/pipeline/steps/credentials-binding/#code-withcredentials-code-bind-credentials-to-variables
                // We want to use different credentials to deploy the stack

                withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "${awsDeployer}"]]) {
                    ansiColor('xterm') {
                        sh '''
                        #!/bin/bash
                        # configure system-wide environment variables and aliases needed for nvm and npm
                        source /etc/profile
                        git submodule update --init --recursive
                        nvm install 22
                        nvm use 22 >/dev/null
                        node --version
                        npm install -g yarn gulp@^4
                        yarn install
                        yarn build
                        bash jenkins/scripts/db-sync.sh ${ENV} ${RUN_MODE}
                        '''
                    }
                }
            }
            post {
              success {
                echo "success! Sync DB for ${environmentToDeploy}."
              }
              failure {
                echo "failed to Sync DB for ${environmentToDeploy}."
              }
              aborted {
                echo "job aborted. Did not Sync to ${environmentToDeploy}."
              }
            }
        }
    }
}