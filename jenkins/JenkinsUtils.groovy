import groovy.json.JsonOutput
/**
 * Hosts the common utility functions for j<PERSON>kins pipelines
 */

/**
 * Get the CFN stack name
 * @param  env [environment name]
 * @return     [CFN stack name wrt environment name]
 */
String getCFNStackName(String env) {
  return "${env}-change-me"
}

/**
 * get the name of the repository
 * @return [Name of the repository]
 */
String getRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}

/**
 * Deploy application in ECS
 * @param env     [env name]
 * @param version [version number]
 */
void ecsDeploy(String env, String version, String stackName) {
  ansiColor('xterm') {
    sh """
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      ecs-service deploy ${stackName} ${version} \
        cfn/templates/service.json cfn/${env}.params.json \
        -e env/${env}/${env}.env \
        -t env/${env}/${env}.tags.json \
        -r us-east-1
    """
  }
}

/**
 * Read json file and return as String required by aws cli
 * @param  filename          [Name of the file]
 * @param  ['ParameterKey'   [Key ]
 * @param  'ParameterValue'] [Value]
 * @return                   [String of key values pairs]
 */
def paramsFromFile(String filename, keyPair = ['ParameterKey', 'ParameterValue']) {
  assert keyPair.size() == 2

  def paramsJson = readJSON(file: filename)

  paramsJson.collect { item ->
    keyPair.collect { key ->
      item.get(key)
    }.join('=')
  }.join(' ')
}

def paramsFromKeyValuePairsFromFile(String filename) {
  def paramsJson = readJSON(file: filename)
  paramsJson.collect { 
    item -> "${item}"
  }.join(" ")
}

void deployAuroraRdsStack(String env) {
  echo "Deploying Aurora RDS Stack for: ${env}"
  def stackName = "${env}-post-security-aurora-rds"
  def parameterOverrides = paramsFromKeyValuePairsFromFile("env/${env}/db.${env}.params.json")
  def tags = paramsFromKeyValuePairsFromFile("env/${env}/${env}.tags.json")
  def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/db.rds-cluster.yml --parameter-overrides ${parameterOverrides} --tags ${tags} --capabilities CAPABILITY_IAM --no-fail-on-empty-changeset"
  echo "Running this command"
  echo "${args}"
  sh "aws cloudformation ${args}"
}

void deployResourceStack(String env) {
  echo "Deploying Resource Stack ${env}"
  def stackName = "${getCFNStackName(env)}-resources"
  def parameterOverrides =
          paramsFromFile("cfn/resourcesCFN.${env}.params.json")
  def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/resourcesCFN.yaml --parameter-overrides ${parameterOverrides} --capabilities CAPABILITY_IAM"
  sh "./jenkins/aws-cloudformation ${args}"
}

return this
