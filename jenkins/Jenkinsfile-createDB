def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null

pipeline {
    // agent none -->> means it should not need any specific agent
    // agent any -->> it will try to run on any node available
    agent { label 'aws-ec2' }

    stages {
        stage("Create DB") {
            agent {
              docker {
                image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
              }     
            }
            steps {
                script {
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                    def etd = env['environmentToDeploy']
                    awsDeployer = ''
                    switch (etd) {
                        case 'dev':
                            awsDeployer = 'dev-amrpwl-aws-deployer'
                            break
                        case 'int':
                            awsDeployer = 'nonprod-amrpwl-aws-deployer'
                            break
                        case 'uat':
                            awsDeployer = 'uat-amrpwl-aws-deployer'
                            break
                        case 'prod':
                            awsDeployer = 'prod-amrpwl-aws-deployer'
                            break
                    }
                    println 'deployer is ' + awsDeployer
                }
                // https://jenkins.io/doc/pipeline/steps/credentials-binding/#code-withcredentials-code-bind-credentials-to-variables
                // We want to use different credentials to deploy the stack
                withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "${awsDeployer}"]]) {
                  script {
                    jenkinsUtils.deployAuroraRdsStack(environmentToDeploy)
                  }
                }
            }
            post {
              success {
                echo "success! Created DB for ${environmentToDeploy}."
              }
              failure {
                echo "failed to create DB for ${environmentToDeploy}."
              }
              aborted {
                echo "job aborted. Did not create DB for ${environmentToDeploy}."
              }
            }
        }
    }
}