def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null



pipeline {
    environment {
        ENV = "${params.environmentToDeploy}"
        MIGRATION_MODE = "${params.mode}"
    }
    // agent none -->> means it should not need any specific agent
    // agent any -->> it will try to run on any node available
    agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
            args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
        }
      }

    stages {
        stage("Migrate DB") {
            steps {
                println "Build"
                script {
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                    def etd = env['environmentToDeploy']
                    awsDeployer = ''
                    switch (etd) {
                        case 'dev':
                            awsDeployer = 'dev-amrpwl-aws-deployer'
                            break
                        case 'int':
                            awsDeployer = 'nonprod-amrpwl-aws-deployer'
                            break
                        case 'uat':
                            awsDeployer = 'uat-amrpwl-aws-deployer'
                            break
                        case 'prod':
                            awsDeployer = 'prod-amrpwl-aws-deployer'
                            break
                    }
                    println 'deployer is ' + awsDeployer
                }
                // https://jenkins.io/doc/pipeline/steps/credentials-binding/#code-withcredentials-code-bind-credentials-to-variables
                // We want to use different credentials to deploy the stack

                withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "${awsDeployer}"]]) {
                    ansiColor('xterm') {
                        sh '''
                        #!/bin/bash
                        # configure system-wide environment variables and aliases needed for nvm and npm
                        source /etc/profile
                        git submodule update --init --recursive
                        nvm install 22
                        nvm use 22 >/dev/null
                        node --version
                        npm install -g yarn gulp@^4
                        yarn install
                        yarn build
                        bash jenkins/scripts/db-migration.sh ${ENV} ${MIGRATION_MODE}
                        '''
                    }
                }
            }
            post {
              success {
                echo "success! Migrated DB for ${environmentToDeploy}."
              }
              failure {
                echo "failed to migrate DB for ${environmentToDeploy}."
              }
              aborted {
                echo "job aborted. Did not migrate to ${environmentToDeploy}."
              }
            }
        }
    }
}