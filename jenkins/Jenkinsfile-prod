import groovy.json.JsonOutput

def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null

// Jenkins deployment pipeline
pipeline {
  agent { label 'aws-ec2' }

  stages {
    stage('Deploying to Production') {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
          args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
        }
      }
      steps {
        echo "Attempting to deploy to Prod"
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
        }
        echo "BUILD_VERSION: ${params.BUILD_VERSION}"

        withCredentials([[
          $class: "AmazonWebServicesCredentialsBinding",
          credentialsId: "prod-amrpwl-aws-deployer"
        ]]) {
          sh "jenkins/scripts/jenkins-deploy.sh prod ${params.BUILD_VERSION}"
        }
        stash 'project'
      }
      post {
        success {
          echo 'success! Deployed to prod.'
        }
        failure {
          echo "failed to deploy to prod."
        }
        aborted {
          echo "job aborted. Did not deploy to prod."
        }
      }
    }
  }
}
