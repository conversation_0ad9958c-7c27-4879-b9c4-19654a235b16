#!/bin/bash -l
set -e
set -o pipefail ## Fail script when a piped command fails

# Migrates the DB to the latest migration versions for a specific environment
# Also will sync the Databases if possible, if the environment IS NOT Prod

if [ "$#" -ne 2 ]; then
  echo "Illegal number of parameters. Accepted are: AWS_ENV migrate/revert"
  exit 42
fi

AWS_ENV=$1
CMD=$2

echo "Using ${AWS_ENV}.env"
# set current working directory to project root
cd $(dirname "$0") && cd ../..
# Read env vars into shell so TypeORM knows configurations
$(cat env/${AWS_ENV}/${AWS_ENV}.env | sed 's/^/export /')

# If database passwords are secured with K<PERSON>, decrypt passwords
eval $($(dirname "$0")/env-decrypt.sh)

# Sync schemas when not on prod
# if [[ $AWS_ENV != "production" ]]; then
#   echo "Syncing Database schemas"
#   npm run db:schema-sync
# fi

if [[ $CMD == "migrate" ]]; then
  npm run db:run-migration
elif [[ $CMD == "revert" ]]; then
  npm run db:revert-migration
else
  echo "Unknown command: $CMD"
  exit 69
fi
