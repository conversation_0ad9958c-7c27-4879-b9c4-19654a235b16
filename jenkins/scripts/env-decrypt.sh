#!/bin/bash

## This script is used to decrypt secure env vars. It returns the 'export' commands to write the decrypted vars.
## Usage from another script: eval $(${SCRIPT_DIR}/env-decrypt.sh)

if [[ -z ${KMS_DATA_KEY} ]]; then
    echo "echo WARN: Missing env var KMS_DATA_KEY. Cannot decrypt env vars"
    exit
fi

if [[ -z `env | grep secure` ]]; then
    echo "echo INFO: No 'secure' env vars to decrypt"
    exit
fi

set -e

source /etc/profile

npm install -g --quiet kms-env@0.3.0 > /dev/null

kms-env decrypt