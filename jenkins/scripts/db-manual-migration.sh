#!/bin/bash -l

##########################################
# NOTE:
# This should only be used when manually running the migration commands from a tunneled in machine
#
# FIXME: the reason we do this is because we couldn't get the jenkins jobs to work to run the migrations for uat and prod.
# if the above can be fixed, that is the preferred way to run the migrations
##########################################

set -e
set -o pipefail ## Fail script when a piped command fails

if [ "$#" -ne 2 ]; then
  echo "Illegal number of parameters. Accepted are: AWS_ENV migrate/revert"
  exit 42
fi

AWS_ENV=$1
CMD=$2

echo "Using ${AWS_ENV}.env"
# set current working directory to project root
cd $(dirname "$0") && cd ../..
# Read env vars into shell so TypeORM knows configurations
$(cat env/${AWS_ENV}/${AWS_ENV}.env | sed 's/^/export /')

# We export the this as the host because the setup_ssh_tunnels.sh will have the SSH tunnels set with this as the host
export TYPEORM_HOST=127.0.0.1
# If database passwords are secured with KMS, decrypt passwords
eval $($(dirname "$0")/env-decrypt.sh)

##########################################
# WARNING - This is syncing databases, even on prod
##########################################

echo "Syncing Database schemas"
npm run db:schema-sync
npm run db:run-migration
