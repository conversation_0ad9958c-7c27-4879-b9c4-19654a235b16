#!/bin/bash -l
set -e
set -o pipefail ## Fail script when a piped command fails

# Syncs the given environments DB Schema with the latest schema from the code

if [ "$#" -ne 2 ]; then
  echo "Illegal number of parameters. Accepted are: AWS_ENV migrate/revert"
  exit 42
fi

AWS_ENV=$1
CMD=$2

echo "Using ${AWS_ENV}.env"
# set current working directory to project root
cd $(dirname "$0") && cd ../..
# Read env vars into shell so TypeORM knows configurations
$(cat env/${AWS_ENV}/${AWS_ENV}.env | sed 's/^/export /')

# If database passwords are secured with K<PERSON>, decrypt passwords
eval $($(dirname "$0")/env-decrypt.sh)

# printenv

if [[ $CMD == "sync" ]]; then
  npm run db:schema-sync && npm run db:run-migration
elif [[ $CMD == "drop" ]]; then
  if [[ $AWS_ENV == "production" ]]; then
    echo "ERROR: NOT ALLOWED TO DROP ON PRODUCTION"
    exit 1
  fi
  npm run db:schema-drop
else
  echo "Unknown command: $CMD"
  exit 69
fi
