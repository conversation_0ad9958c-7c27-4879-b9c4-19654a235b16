#!/usr/bin/env bash

# if any step in script fails - stop the script
# https://ss64.com/bash/set.html
set -e

# do not show actual command being ran
# https://ss64.com/bash/set.html
set +x

# source profile for general node - for LoyaltyOne's general node
source /etc/profile

if [ "$#" -ne 2 ]; then
    echo "Illegal number of parameters"
    exit 42
fi

if ! [[ $1 =~ dev|int|uat|prod ]]; then
    echo "usage: jenkins-deploy.sh dev|int|uat|prod"
    exit 69
fi

AWS_ENV=$1
CURRENT_APP_VERSION=$2
STACK_NAME=${AWS_ENV}-post-security-manager

# set current working directory to project root
cd $(dirname "$0") && cd ../..

pip3 install cfn-flip==1.3.0 --user
cfn-flip cfn/templates/service.yaml cfn/templates/service.json

ecs-service deploy ${STACK_NAME} ${CURRENT_APP_VERSION} cfn/templates/service.json env/${AWS_ENV}/${AWS_ENV}.params.json -e env/${AWS_ENV}/${AWS_ENV}.env -t env/${AWS_ENV}/${AWS_ENV}.tags.json -r us-east-1
